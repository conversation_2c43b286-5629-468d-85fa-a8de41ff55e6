// Generated by wxt
export {}
declare global {
  const API_CONFIG: typeof import('E:/fire-wall-guard-v2/frontend/components/config')['API_CONFIG']
  const ContentScriptContext: typeof import('wxt/utils/content-script-context')['ContentScriptContext']
  const EffectScope: typeof import('vue')['EffectScope']
  const InvalidMatchPattern: typeof import('wxt/utils/match-patterns')['InvalidMatchPattern']
  const MatchPattern: typeof import('wxt/utils/match-patterns')['MatchPattern']
  const browser: typeof import('wxt/browser')['browser']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createIframeUi: typeof import('wxt/utils/content-script-ui/iframe')['createIframeUi']
  const createIntegratedUi: typeof import('wxt/utils/content-script-ui/integrated')['createIntegratedUi']
  const createShadowRootUi: typeof import('wxt/utils/content-script-ui/shadow-root')['createShadowRootUi']
  const customRef: typeof import('vue')['customRef']
  const defineAppConfig: typeof import('wxt/utils/define-app-config')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineBackground: typeof import('wxt/utils/define-background')['defineBackground']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineContentScript: typeof import('wxt/utils/define-content-script')['defineContentScript']
  const defineUnlistedScript: typeof import('wxt/utils/define-unlisted-script')['defineUnlistedScript']
  const defineWxtPlugin: typeof import('wxt/utils/define-wxt-plugin')['defineWxtPlugin']
  const detectBrowser: typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['detectBrowser']
  const effectScope: typeof import('vue')['effectScope']
  const fakeBrowser: typeof import('wxt/testing')['fakeBrowser']
  const getApiUrl: typeof import('E:/fire-wall-guard-v2/frontend/components/config')['getApiUrl']
  const getBrowserAPI: typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['getBrowserAPI']
  const getBrowserSpecificErrorMessage: typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['getBrowserSpecificErrorMessage']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const injectScript: typeof import('wxt/utils/inject-script')['injectScript']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isSpecialPage: typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['isSpecialPage']
  const markRaw: typeof import('vue')['markRaw']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const storage: typeof import('wxt/utils/storage')['storage']
  const testApiConnection: typeof import('E:/fire-wall-guard-v2/frontend/components/config')['testApiConnection']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const updateApiConfig: typeof import('E:/fire-wall-guard-v2/frontend/components/config')['updateApiConfig']
  const useAppConfig: typeof import('wxt/utils/app-config')['useAppConfig']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useId: typeof import('vue')['useId']
  const useModel: typeof import('vue')['useModel']
  const useSlots: typeof import('vue')['useSlots']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Browser } from 'wxt/browser'
  import('wxt/browser')
  // @ts-ignore
  export type { StorageArea, WxtStorage, WxtStorageItem, StorageItemKey, StorageAreaChanges, MigrationError } from 'wxt/utils/storage'
  import('wxt/utils/storage')
  // @ts-ignore
  export type { WxtWindowEventMap } from 'wxt/utils/content-script-context'
  import('wxt/utils/content-script-context')
  // @ts-ignore
  export type { IframeContentScriptUi, IframeContentScriptUiOptions } from 'wxt/utils/content-script-ui/iframe'
  import('wxt/utils/content-script-ui/iframe')
  // @ts-ignore
  export type { IntegratedContentScriptUi, IntegratedContentScriptUiOptions } from 'wxt/utils/content-script-ui/integrated'
  import('wxt/utils/content-script-ui/integrated')
  // @ts-ignore
  export type { ShadowRootContentScriptUi, ShadowRootContentScriptUiOptions } from 'wxt/utils/content-script-ui/shadow-root'
  import('wxt/utils/content-script-ui/shadow-root')
  // @ts-ignore
  export type { ContentScriptUi, ContentScriptUiOptions, ContentScriptOverlayAlignment, ContentScriptAppendMode, ContentScriptInlinePositioningOptions, ContentScriptOverlayPositioningOptions, ContentScriptModalPositioningOptions, ContentScriptPositioningOptions, ContentScriptAnchoredOptions, AutoMountOptions, StopAutoMount, AutoMount } from 'wxt/utils/content-script-ui/types'
  import('wxt/utils/content-script-ui/types')
  // @ts-ignore
  export type { WxtAppConfig } from 'wxt/utils/define-app-config'
  import('wxt/utils/define-app-config')
  // @ts-ignore
  export type { ScriptPublicPath, InjectScriptOptions } from 'wxt/utils/inject-script'
  import('wxt/utils/inject-script')
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { BrowserInfo } from 'E:/fire-wall-guard-v2/frontend/components/browserUtils'
  import('E:/fire-wall-guard-v2/frontend/components/browserUtils')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly API_CONFIG: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/config')['API_CONFIG']>
    readonly ContentScriptContext: UnwrapRef<typeof import('wxt/utils/content-script-context')['ContentScriptContext']>
    readonly EffectScope: UnwrapRef<typeof import('vue')['EffectScope']>
    readonly InvalidMatchPattern: UnwrapRef<typeof import('wxt/utils/match-patterns')['InvalidMatchPattern']>
    readonly MatchPattern: UnwrapRef<typeof import('wxt/utils/match-patterns')['MatchPattern']>
    readonly browser: UnwrapRef<typeof import('wxt/browser')['browser']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createApp: UnwrapRef<typeof import('vue')['createApp']>
    readonly createIframeUi: UnwrapRef<typeof import('wxt/utils/content-script-ui/iframe')['createIframeUi']>
    readonly createIntegratedUi: UnwrapRef<typeof import('wxt/utils/content-script-ui/integrated')['createIntegratedUi']>
    readonly createShadowRootUi: UnwrapRef<typeof import('wxt/utils/content-script-ui/shadow-root')['createShadowRootUi']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly defineAppConfig: UnwrapRef<typeof import('wxt/utils/define-app-config')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineBackground: UnwrapRef<typeof import('wxt/utils/define-background')['defineBackground']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineContentScript: UnwrapRef<typeof import('wxt/utils/define-content-script')['defineContentScript']>
    readonly defineUnlistedScript: UnwrapRef<typeof import('wxt/utils/define-unlisted-script')['defineUnlistedScript']>
    readonly defineWxtPlugin: UnwrapRef<typeof import('wxt/utils/define-wxt-plugin')['defineWxtPlugin']>
    readonly detectBrowser: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['detectBrowser']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly fakeBrowser: UnwrapRef<typeof import('wxt/testing')['fakeBrowser']>
    readonly getApiUrl: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/config')['getApiUrl']>
    readonly getBrowserAPI: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['getBrowserAPI']>
    readonly getBrowserSpecificErrorMessage: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['getBrowserSpecificErrorMessage']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectScript: UnwrapRef<typeof import('wxt/utils/inject-script')['injectScript']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isSpecialPage: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/browserUtils')['isSpecialPage']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly onWatcherCleanup: UnwrapRef<typeof import('vue')['onWatcherCleanup']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly storage: UnwrapRef<typeof import('wxt/utils/storage')['storage']>
    readonly testApiConnection: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/config')['testApiConnection']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly updateApiConfig: UnwrapRef<typeof import('E:/fire-wall-guard-v2/frontend/components/config')['updateApiConfig']>
    readonly useAppConfig: UnwrapRef<typeof import('wxt/utils/app-config')['useAppConfig']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
  }
}
