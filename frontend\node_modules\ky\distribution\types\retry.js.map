{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../source/types/retry.ts"], "names": [], "mappings": "", "sourcesContent": ["export type RetryOptions = {\n\t/**\n\tThe number of times to retry failed requests.\n\n\t@default 2\n\t*/\n\tlimit?: number;\n\n\t/**\n\tThe HTTP methods allowed to retry.\n\n\t@default ['get', 'put', 'head', 'delete', 'options', 'trace']\n\t*/\n\tmethods?: string[];\n\n\t/**\n\tThe HTTP status codes allowed to retry.\n\n\t@default [408, 413, 429, 500, 502, 503, 504]\n\t*/\n\tstatusCodes?: number[];\n\n\t/**\n\tThe HTTP status codes allowed to retry with a `Retry-After` header.\n\n\t@default [413, 429, 503]\n\t*/\n\tafterStatusCodes?: number[];\n\n\t/**\n\tIf the `Retry-After` header is greater than `maxRetryAfter`, the request will be canceled.\n\n\t@default Infinity\n\t*/\n\tmaxRetryAfter?: number;\n\n\t/**\n\tThe upper limit of the delay per retry in milliseconds.\n\tTo clamp the delay, set `backoffLimit` to 1000, for example.\n\n\tBy default, the delay is calculated in the following way:\n\n\t```\n\t0.3 * (2 ** (attemptCount - 1)) * 1000\n\t```\n\n\tThe delay increases exponentially.\n\n\t@default Infinity\n\t*/\n\tbackoffLimit?: number;\n\n\t/**\n\tA function to calculate the delay between retries given `attemptCount` (starts from 1).\n\n\t@default attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000\n\t*/\n\tdelay?: (attemptCount: number) => number;\n};\n"]}