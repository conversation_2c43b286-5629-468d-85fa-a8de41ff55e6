{"version": 3, "file": "HTTPError.js", "sourceRoot": "", "sources": ["../../source/errors/HTTPError.ts"], "names": [], "mappings": "AAIA,MAAM,OAAO,SAAuB,SAAQ,KAAK;IACzC,QAAQ,CAAgB;IACxB,OAAO,CAAY;IACnB,OAAO,CAAoB;IAElC,YAAY,QAAkB,EAAE,OAAgB,EAAE,OAA0B;QAC3E,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/E,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;QACxC,MAAM,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,eAAe,MAAM,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC;QAErE,KAAK,CAAC,uBAAuB,MAAM,KAAK,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAEzE,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;CACD", "sourcesContent": ["import type {NormalizedOptions} from '../types/options.js';\nimport type {KyRequest} from '../types/request.js';\nimport type {KyResponse} from '../types/response.js';\n\nexport class HTTPError<T = unknown> extends Error {\n\tpublic response: KyResponse<T>;\n\tpublic request: KyRequest;\n\tpublic options: NormalizedOptions;\n\n\tconstructor(response: Response, request: Request, options: NormalizedOptions) {\n\t\tconst code = (response.status || response.status === 0) ? response.status : '';\n\t\tconst title = response.statusText || '';\n\t\tconst status = `${code} ${title}`.trim();\n\t\tconst reason = status ? `status code ${status}` : 'an unknown error';\n\n\t\tsuper(`Request failed with ${reason}: ${request.method} ${request.url}`);\n\n\t\tthis.name = 'HTTPError';\n\t\tthis.response = response;\n\t\tthis.request = request;\n\t\tthis.options = options;\n\t}\n}\n"]}