<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { getApiUrl, API_CONFIG } from './config';
import { marked } from 'marked';  // 添加 marked 导入
import { detectBrowser, getBrowserAPI, isSpecialPage } from './browserUtils';

// 定义事件
const emit = defineEmits(['back']);

// 存储键名
const STORAGE_KEYS = {
  PARSE_RESULT: 'screen_capture_parse_result',
  VALIDATION_RESPONSE: 'screen_capture_validation_response',
  SUBMIT_SUCCESS: 'screen_capture_submit_success'
};

const screenshotData = ref<string | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const currentUrl = ref<string>('');
const isCurrentPageSpecial = ref(false);
const apiLoading = ref(false);
const apiError = ref<string | null>(null);
const parseResult = ref<Array<{key: string, value: string}>>([]);
const showParseResult = ref(false);

// 浏览器信息
const browserInfo = ref(detectBrowser());
const browserAPI = getBrowserAPI();

// 添加新的状态变量
const submitSuccess = ref(false);
const validationResponse = ref<string | null>(null);

// 添加计算属性：将validationResponse转换为HTML
const parsedValidationResponse = computed(() => {
  if (!validationResponse.value) return '';
  return marked(validationResponse.value);
});

// 保存数据到localStorage
function saveToStorage() {
  try {
    localStorage.setItem(STORAGE_KEYS.PARSE_RESULT, JSON.stringify(parseResult.value));
    localStorage.setItem(STORAGE_KEYS.VALIDATION_RESPONSE, validationResponse.value || '');
    localStorage.setItem(STORAGE_KEYS.SUBMIT_SUCCESS, JSON.stringify(submitSuccess.value));
  } catch (err) {
    console.error('保存数据到localStorage失败:', err);
  }
}

// 从localStorage加载数据
function loadFromStorage() {
  try {
    const storedParseResult = localStorage.getItem(STORAGE_KEYS.PARSE_RESULT);
    const storedValidationResponse = localStorage.getItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    const storedSubmitSuccess = localStorage.getItem(STORAGE_KEYS.SUBMIT_SUCCESS);

    if (storedParseResult) {
      parseResult.value = JSON.parse(storedParseResult);
      showParseResult.value = true;
    }
    if (storedValidationResponse) {
      validationResponse.value = storedValidationResponse;
    }
    if (storedSubmitSuccess) {
      submitSuccess.value = JSON.parse(storedSubmitSuccess);
    }
  } catch (err) {
    console.error('从localStorage加载数据失败:', err);
  }
}

// 清除存储的数据
function clearStorage() {
  try {
    localStorage.removeItem(STORAGE_KEYS.PARSE_RESULT);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    localStorage.removeItem(STORAGE_KEYS.SUBMIT_SUCCESS);
    
    parseResult.value = [];
    validationResponse.value = null;
    submitSuccess.value = false;
    showParseResult.value = false;
    screenshotData.value = null;
    apiError.value = null;
  } catch (err) {
    console.error('清除localStorage数据失败:', err);
  }
}

// 编辑键值对
function editItem(index: number, field: 'key' | 'value', value: string) {
  if (index >= 0 && index < parseResult.value.length) {
    parseResult.value[index][field] = value;
    saveToStorage();
  }
}

// 删除键值对
function removeItem(index: number) {
  if (index >= 0 && index < parseResult.value.length) {
    parseResult.value.splice(index, 1);
    saveToStorage();
  }
}

// 添加新的键值对
function addNewItem() {
  parseResult.value.push({ key: '', value: '' });
  saveToStorage();
}

// 上移项目
function moveItemUp(index: number) {
  if (index > 0) {
    const item = parseResult.value[index];
    parseResult.value.splice(index, 1);
    parseResult.value.splice(index - 1, 0, item);
    saveToStorage();
  }
}

// 下移项目
function moveItemDown(index: number) {
  if (index < parseResult.value.length - 1) {
    const item = parseResult.value[index];
    parseResult.value.splice(index, 1);
    parseResult.value.splice(index + 1, 0, item);
    saveToStorage();
  }
}

// 返回主页
function goBack() {
  emit('back');
}

// 检查当前页面类型
onMounted(async () => {
  // 加载存储的数据
  loadFromStorage();

  try {
    const tabs = await browserAPI.getCurrentTab();
    if (tabs && tabs.length > 0 && tabs[0].url) {
      currentUrl.value = tabs[0].url;

      // 使用浏览器特定的特殊页面检查
      isCurrentPageSpecial.value = isSpecialPage(currentUrl.value);

      console.log(`当前浏览器: ${browserInfo.value.browserName}`);
      console.log(`当前页面: ${currentUrl.value}`);
      console.log(`是否为特殊页面: ${isCurrentPageSpecial.value}`);
      console.log(`支持截图: ${browserInfo.value.supportsScreenshot}`);
    }
  } catch (err) {
    console.error('获取当前页面信息失败', err);
    error.value = `获取当前页面信息失败: ${err instanceof Error ? err.message : String(err)}`;
  }
});

// 通过背景脚本截取当前活动标签页的屏幕
async function captureScreen() {
  try {
    loading.value = true;
    error.value = null;
    parseResult.value = [];
    showParseResult.value = false;

    // 检查浏览器是否支持截图
    if (!browserInfo.value.supportsScreenshot) {
      throw new Error(`${browserInfo.value.browserName} 浏览器不支持截图功能`);
    }

    // 发送消息给背景脚本请求截图
    const response = await browser.runtime.sendMessage({
      action: 'captureScreen',
      browserInfo: browserInfo.value
    });

    if (response.success) {
      screenshotData.value = response.dataUrl;
      console.log(`截图成功 (${browserInfo.value.browserName})`);
    } else {
      throw new Error(response.error || '截图失败');
    }
  } catch (err) {
    console.error('截图失败:', err);
    const errorMessage = err instanceof Error ? err.message : '截图时发生未知错误';
    error.value = `${browserInfo.value.browserName}: ${errorMessage}`;
  } finally {
    loading.value = false;
  }
}

// 下载截图
function downloadScreenshot() {
  if (!screenshotData.value) return;
  
  const a = document.createElement('a');
  a.href = screenshotData.value;
  
  // 生成文件名: screenshot_日期_时间.png
  const date = new Date();
  const fileName = `screenshot_${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}_${date.getHours().toString().padStart(2, '0')}${date.getMinutes().toString().padStart(2, '0')}${date.getSeconds().toString().padStart(2, '0')}.png`;
  
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// 发送截图到后端进行解析
async function sendToBackend() {
  if (!screenshotData.value) return;
  
  try {
    apiLoading.value = true;
    apiError.value = null;
    
    // 将Base64数据URL转换为Blob
    const base64Data = screenshotData.value.split(',')[1];
    const byteCharacters = atob(base64Data);
    const byteArrays = [];
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteArrays.push(byteCharacters.charCodeAt(i));
    }
    
    const byteArray = new Uint8Array(byteArrays);
    const blob = new Blob([byteArray], { type: 'image/png' });
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('image', blob, 'screenshot.png');
    
    // 发送请求到后端API
    const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.IMAGE_PARSE), {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`服务器响应错误: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 将对象转换为数组形式
      parseResult.value = Object.entries(result.data).map(([key, value]) => ({
        key,
        value: value as string
      }));
      showParseResult.value = true;
      saveToStorage();
    } else {
      throw new Error(result.msg || '解析失败');
    }
    
  } catch (err) {
    console.error('发送截图到后端失败:', err);
    apiError.value = err instanceof Error ? err.message : '发送截图时发生未知错误';
  } finally {
    apiLoading.value = false;
  }
}

// 添加发送数据到后端获取修改建议的函数
async function submitToBackend() {
  if (parseResult.value.length === 0) {
    apiError.value = '没有数据可提交';
    return;
  }
  
  try {
    apiLoading.value = true;
    apiError.value = null;
    submitSuccess.value = false;
    validationResponse.value = null;
    
    // 检查数据有效性
    const invalidEntries = parseResult.value.filter(item => !item.key.trim());
    if (invalidEntries.length > 0) {
      throw new Error('存在空的键，请检查数据');
    }
    
    // 准备要发送的数据
    const dataToSend = {
      items: parseResult.value.map(item => ({
        key: item.key.trim(),
        value: item.value.trim()
      }))
    };
    
    console.log('发送数据到后端:', dataToSend);
    
    // 发送请求到后端API
    const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.FORM_VALIDATE), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dataToSend)
    });
    
    const result = await response.json();
    console.log('后端响应:', result);
    
    if (result.code === 400) {
      // 当状态码为400时，显示修改建议
      submitSuccess.value = true;
      validationResponse.value = result.data || '未收到具体的修改建议。';
      // 保存到localStorage
      saveToStorage();
    } else {
      // 其他状态码视为错误
      throw new Error(result.msg || '提交失败');
    }
    
  } catch (err) {
    console.error('发送数据到后端失败:', err);
    apiError.value = err instanceof Error ? err.message : '发送数据时发生未知错误';
    submitSuccess.value = false;
    validationResponse.value = null;
    // 保存错误状态
    saveToStorage();
  } finally {
    apiLoading.value = false;
  }
}
</script>

<template>
  <div class="screen-capture">
    <div class="header">
      <button @click="goBack" class="back-btn">
        &larr; 返回
      </button>
      <h2>页面截图工具</h2>
      <div class="browser-info">
        <span class="browser-badge" :class="browserInfo.browserName.toLowerCase()">
          {{ browserInfo.browserName }}
        </span>
      </div>
    </div>

    <div v-if="isCurrentPageSpecial" class="warning">
      <p>⚠️ 提示：当前页面是{{ browserInfo.browserName }}浏览器特殊页面，可能无法正常截图。</p>
      <p>建议在普通网页上使用此扩展。</p>
    </div>

    <div v-if="!browserInfo.supportsScreenshot" class="error">
      <p>❌ 错误：当前{{ browserInfo.browserName }}浏览器不支持截图功能。</p>
    </div>
    
    <div class="controls">
      <button 
        @click="captureScreen" 
        :disabled="loading"
        class="capture-btn"
      >
        {{ loading ? '正在截图...' : '截取当前页面' }}
      </button>
    </div>
    
    <div v-if="error" class="error">
      {{ error }}
      <div v-if="isCurrentPageSpecial" class="error-hint">
        (由于{{ browserInfo.browserName }}浏览器安全限制，某些特殊页面如新标签页、设置页等可能无法截图)
      </div>
    </div>
    
    <div v-if="screenshotData" class="screenshot-container">
      <div class="screenshot-header">
        <h3>截图预览：</h3>
        <div class="action-buttons">
          <button @click="downloadScreenshot" class="download-btn">下载截图</button>
          <button 
            @click="sendToBackend" 
            :disabled="apiLoading" 
            class="parse-btn"
          >
            {{ apiLoading ? '解析中...' : '解析内容' }}
          </button>
        </div>
      </div>
      <img :src="screenshotData" alt="页面截图" class="screenshot-img" />
      
      <div v-if="apiError" class="error">
        解析失败: {{ apiError }}
      </div>
      
      <div v-if="showParseResult" class="parse-result">
        <div class="parse-result-header">
          <h3>解析结果：共 {{ parseResult.length }} 项</h3>
          <div class="parse-result-actions">
            <button @click="addNewItem" class="add-btn">
              + 添加字段
            </button>
            <button v-if="parseResult.length > 0" @click="clearStorage" class="clear-btn">
              清除数据
            </button>
          </div>
        </div>
        
        <div class="parse-result-list">
          <div v-if="parseResult.length === 0" class="no-result">
            未识别到有效信息
          </div>
          <div v-else>
            <div v-for="(item, index) in parseResult" :key="index" class="parse-result-item">
              <div class="item-order-buttons">
                <button 
                  @click="moveItemUp(index)" 
                  :disabled="index === 0"
                  class="order-btn up-btn"
                  title="上移"
                >
                  ▲
                </button>
                <button 
                  @click="moveItemDown(index)" 
                  :disabled="index === parseResult.length - 1"
                  class="order-btn down-btn"
                  title="下移"
                >
                  ▼
                </button>
              </div>
              <div class="parse-result-inputs">
                <input 
                  type="text" 
                  :value="item.key" 
                  @input="e => editItem(index, 'key', (e.target as HTMLInputElement).value)"
                  placeholder="字段名"
                  class="key-input"
                />
                <span class="separator">:</span>
                <div class="value-input-container">
                  <input 
                    type="text" 
                    :value="item.value" 
                    @input="e => editItem(index, 'value', (e.target as HTMLInputElement).value)"
                    placeholder="值"
                    class="value-input"
                  />
                  <span v-if="item.key && !item.value" class="empty-value-hint" title="未识别到该字段的值">
                    ⚠️
                  </span>
                </div>
              </div>
              <button @click="removeItem(index)" class="remove-btn" title="删除此项">
                &times;
              </button>
            </div>
          </div>
        </div>

        <!-- 添加提交按钮和验证结果显示 -->
        <div class="form-actions">
          <button 
            @click="submitToBackend" 
            :disabled="apiLoading || parseResult.length === 0"
            class="submit-btn"
          >
            <span v-if="apiLoading" class="loading-spinner"></span>
            {{ apiLoading ? '提交中...' : '提交数据' }}
          </button>
        </div>
        
        <div v-if="submitSuccess" class="success">
          <strong>成功！</strong> 数据已成功提交到后端。
          <div v-if="validationResponse" class="validation-response">
            <strong>修改建议：</strong>
            <div class="markdown-content" v-html="parsedValidationResponse"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.screen-capture {
  padding: 1rem;
  max-width: 500px;
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.browser-info {
  display: flex;
  align-items: center;
}

.browser-badge {
  background-color: #f0f0f0;
  color: #333;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.browser-badge.firefox {
  background-color: #ff9500;
  color: white;
}

.browser-badge.chrome {
  background-color: #4285f4;
  color: white;
}

.browser-badge.edge {
  background-color: #0078d4;
  color: white;
}

.back-btn {
  background: none;
  border: none;
  color: #2196F3;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.3rem 0.5rem;
  margin-right: 1rem;
  border-radius: 4px;
}

.back-btn:hover {
  background-color: #f0f0f0;
}

h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.controls {
  margin-bottom: 1rem;
}

.capture-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.capture-btn:hover {
  background-color: #45a049;
}

.capture-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  border: 1px solid #ffeeba;
}

.error {
  color: #f44336;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
}

.error-hint {
  font-size: 0.85rem;
  margin-top: 0.5rem;
  color: #666;
}

.screenshot-container {
  margin-top: 1rem;
}

.screenshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

h3 {
  font-size: 1.1rem;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.download-btn, .parse-btn {
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  font-size: 0.9rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.download-btn {
  background-color: #2196F3;
}

.download-btn:hover {
  background-color: #0b7dda;
}

.parse-btn {
  background-color: #FF9800;
}

.parse-btn:hover {
  background-color: #e68a00;
}

.parse-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.screenshot-img {
  max-width: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.parse-result {
  margin-top: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  background-color: #f9f9f9;
}

.no-result {
  color: #666;
  font-style: italic;
  padding: 0.5rem 0;
}

.result-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.5rem;
}

.result-table th, .result-table td {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: left;
}

.result-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.result-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.result-table tr:hover {
  background-color: #f1f1f1;
}

.parse-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.parse-result-actions {
  display: flex;
  gap: 0.5rem;
}

.add-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  font-size: 0.9rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.clear-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  font-size: 0.9rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-btn:hover {
  background-color: #d32f2f;
}

.parse-result-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.parse-result-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.parse-result-item:hover {
  border-color: #bbdefb;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.item-order-buttons {
  display: flex;
  flex-direction: column;
  margin-right: 0.5rem;
}

.order-btn {
  background: none;
  border: none;
  color: #757575;
  font-size: 0.7rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.order-btn:hover:not(:disabled) {
  color: #2196F3;
}

.order-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.up-btn {
  margin-bottom: 2px;
}

.parse-result-inputs {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.key-input, .value-input {
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.key-input:focus, .value-input:focus {
  border-color: #2196F3;
  outline: none;
}

.key-input {
  width: 40%;
  margin-right: 0.5rem;
  min-width: 80px;
}

.separator {
  margin: 0 0.5rem;
  color: #666;
  flex-shrink: 0;
}

.value-input-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.empty-value-hint {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #ff9800;
  cursor: help;
}

.value-input {
  width: 100%;
  padding-right: 30px; /* 为图标预留空间 */
}

/* 当输入框获得焦点时，确保图标仍然可见 */
.value-input:focus + .empty-value-hint {
  z-index: 1;
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0.5rem;
  margin-left: 0.5rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  color: #d32f2f;
  background-color: #ffebee;
}

.no-result {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.submit-btn {
  background-color: #FF9800;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover {
  background-color: #e68a00;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.success {
  color: #4CAF50;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #e8f5e9;
  border-radius: 4px;
  text-align: center;
  border-left: 4px solid #4CAF50;
}

.validation-response {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #2196F3;
}

.validation-response :deep(p) {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.validation-response :deep(ul), 
.validation-response :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.validation-response :deep(li) {
  margin: 0.25rem 0;
}

.validation-response :deep(strong) {
  color: #1976D2;
}

.validation-response :deep(code) {
  background-color: #E3F2FD;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.validation-response :deep(blockquote) {
  margin: 0.5rem 0;
  padding-left: 1rem;
  border-left: 3px solid #90CAF9;
  color: #546E7A;
}

.markdown-content {
  font-size: 0.95rem;
  color: #333;
}
</style> 