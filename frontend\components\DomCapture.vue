<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { getApiUrl, API_CONFIG } from './config';
import { marked } from 'marked';

// 定义事件
const emit = defineEmits(['back']);

// 存储键名
const STORAGE_KEYS = {
  FORM_DATA: 'dom_capture_form_data',
  VALIDATION_RESPONSE: 'dom_capture_validation_response',
  SUBMIT_SUCCESS: 'dom_capture_submit_success'
};

// 状态变量
const loading = ref(false);
const error = ref<string | null>(null);
const formData = ref<Array<{key: string, value: string}>>([]);
const currentUrl = ref<string>('');
const apiLoading = ref(false);
const apiError = ref<string | null>(null);
const submitSuccess = ref(false);
const validationResponse = ref<string | null>(null);

// 计算属性：将validationResponse转换为HTML
const parsedValidationResponse = computed(() => {
  if (!validationResponse.value) return '';
  return marked(validationResponse.value);
});

// 保存数据到localStorage
function saveToStorage() {
  try {
    localStorage.setItem(STORAGE_KEYS.FORM_DATA, JSON.stringify(formData.value));
    localStorage.setItem(STORAGE_KEYS.VALIDATION_RESPONSE, validationResponse.value || '');
    localStorage.setItem(STORAGE_KEYS.SUBMIT_SUCCESS, JSON.stringify(submitSuccess.value));
  } catch (err) {
    console.error('保存数据到localStorage失败:', err);
  }
}

// 从localStorage加载数据
function loadFromStorage() {
  try {
    const storedFormData = localStorage.getItem(STORAGE_KEYS.FORM_DATA);
    const storedValidationResponse = localStorage.getItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    const storedSubmitSuccess = localStorage.getItem(STORAGE_KEYS.SUBMIT_SUCCESS);

    if (storedFormData) {
      formData.value = JSON.parse(storedFormData);
    }
    if (storedValidationResponse) {
      validationResponse.value = storedValidationResponse;
    }
    if (storedSubmitSuccess) {
      submitSuccess.value = JSON.parse(storedSubmitSuccess);
    }
  } catch (err) {
    console.error('从localStorage加载数据失败:', err);
  }
}

// 清除存储的数据
function clearStorage() {
  try {
    // 清除 localStorage
    localStorage.removeItem(STORAGE_KEYS.FORM_DATA);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    localStorage.removeItem(STORAGE_KEYS.SUBMIT_SUCCESS);
    
    // 重置组件状态
    formData.value = [];
    validationResponse.value = null;
    submitSuccess.value = false;
    apiError.value = null;
  } catch (err) {
    console.error('清除localStorage数据失败:', err);
  }
}

// 返回主页
function goBack() {
  emit('back');
}

// 在组件挂载时获取当前页面信息和存储的数据
onMounted(async () => {
  // 加载存储的数据
  loadFromStorage();
  
  try {
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    if (tabs && tabs.length > 0 && tabs[0].url) {
      currentUrl.value = tabs[0].url;
      
      // 检查是否为特殊页面
      const isSpecialPage = 
        currentUrl.value === '' || 
        currentUrl.value === 'about:blank' || 
        currentUrl.value.startsWith('chrome://') || 
        currentUrl.value.startsWith('chrome-extension://') ||
        currentUrl.value.startsWith('edge://') ||
        currentUrl.value.startsWith('about:');
      
      if (isSpecialPage) {
        error.value = '当前页面是浏览器特殊页面，无法解析DOM结构。';
        return;
      }
    }
  } catch (err) {
    console.error('获取当前页面信息失败', err);
    error.value = '获取当前页面信息失败';
  }
});

// 解析页面DOM结构
async function parseDom() {
  try {
    loading.value = true;
    error.value = null;
    
    const tabs = await browser.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      throw new Error('无法获取当前标签页');
    }
    
    // 在当前标签页执行脚本，解析DOM结构
    const result = await browser.scripting.executeScript({
      target: { tabId: tabs[0].id as number },
      func: extractFormData,
    });
    
    console.log('解析结果原始数据:', result);
    
    if (result && result[0] && result[0].result) {
      // 确保结果中的每一项都有key和value
      formData.value = result[0].result.map((item, index) => {
        // 如果项目不是对象或缺少key属性，创建一个默认的key
        if (typeof item !== 'object' || !item.key) {
          return {
            key: `字段_${index + 1}`,
            value: typeof item === 'object' && item.value ? item.value : String(item)
          };
        }
        return item;
      });
      
      // 保存到localStorage
      saveToStorage();
      
      // 调试：打印处理后的数据
      console.log('处理后的formData:', formData.value);
    }
    
    if (formData.value.length === 0) {
      error.value = '未在页面中找到可解析的表单数据';
    }
  } catch (err) {
    console.error('解析DOM结构失败:', err);
    error.value = err instanceof Error ? err.message : '解析DOM结构时发生未知错误';
  } finally {
    loading.value = false;
  }
}

// 发送数据到后端
async function submitToBackend() {
  if (formData.value.length === 0) {
    apiError.value = '没有数据可提交';
    return;
  }
  
  try {
    apiLoading.value = true;
    apiError.value = null;
    submitSuccess.value = false;
    validationResponse.value = null;
    
    // 检查数据有效性
    const invalidEntries = formData.value.filter(item => !item.key.trim() || !item.value.trim());
    if (invalidEntries.length > 0) {
      throw new Error('存在空的键或值，请检查数据');
    }
    
    // 准备要发送的数据
    const dataToSend = {
      items: formData.value.map(item => ({
        key: item.key.trim(),
        value: item.value.trim()
      }))
    };
    
    console.log('发送数据到后端:', dataToSend);
    
    // 发送请求到后端API
    const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.FORM_VALIDATE), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dataToSend)
    });
    
    const result = await response.json();
    console.log('后端响应:', result);
    
    if (result.code === 400) {
      // 当状态码为400时，显示修改建议
      submitSuccess.value = true;
      validationResponse.value = result.data || '未收到具体的修改建议。';
      // 保存到localStorage
      saveToStorage();
    } else {
      // 其他状态码视为错误，显示错误信息
      throw new Error(result.msg || '提交失败');
    }
    
  } catch (err) {
    console.error('发送数据到后端失败:', err);
    apiError.value = err instanceof Error ? err.message : '发送数据时发生未知错误';
    submitSuccess.value = false;
    validationResponse.value = null;
    // 保存错误状态
    saveToStorage();
  } finally {
    apiLoading.value = false;
  }
}

// 编辑键值对
function editItem(index: number, field: 'key' | 'value', value: string) {
  if (index >= 0 && index < formData.value.length) {
    formData.value[index][field] = value;
    // 保存更改
    saveToStorage();
  }
}

// 删除键值对
function removeItem(index: number) {
  if (index >= 0 && index < formData.value.length) {
    formData.value.splice(index, 1);
    // 保存更改
    saveToStorage();
  }
}

// 添加新的键值对
function addNewItem() {
  formData.value.push({ key: '', value: '' });
  // 保存更改
  saveToStorage();
}

// 上移项目
function moveItemUp(index: number) {
  if (index > 0) {
    const item = formData.value[index];
    formData.value.splice(index, 1);
    formData.value.splice(index - 1, 0, item);
    // 保存更改
    saveToStorage();
  }
}

// 下移项目
function moveItemDown(index: number) {
  if (index < formData.value.length - 1) {
    const item = formData.value[index];
    formData.value.splice(index, 1);
    formData.value.splice(index + 1, 0, item);
    // 保存更改
    saveToStorage();
  }
}

// 在页面中执行的脚本，用于提取表单数据
function extractFormData() {
  try {
    // 存储提取的键值对
    const formData = [];
    const processedElements = new Set(); // 跟踪已处理的元素
    
    /**
     * 尝试从元素中提取键名
     * @param {HTMLElement} element - 输入元素
     * @param {HTMLElement} context - 上下文元素（如表单）
     * @returns {string} 提取的键名
     */
    function extractKeyFromElement(element, context) {
      let key = '';
      
      // 1. 通过label的for属性
      if (element.id) {
        const labelSelector = `label[for="${element.id}"]`;
        const label = (context || document).querySelector(labelSelector);
        if (label && label.textContent) {
          return label.textContent.trim().replace(/[:：*]$/, '');
        }
      }
      
      // 2. 查找父元素中的label元素
      const parentLabel = element.closest('label');
      if (parentLabel) {
        const labelText = Array.from(parentLabel.childNodes)
          .filter(node => node.nodeType === 3) // 只获取文本节点
          .map(node => node.textContent.trim())
          .join(' ')
          .replace(/[:：*]$/, '');
        
        if (labelText) {
          return labelText;
        }
      }
      
      // 3. 查找父元素中的文本节点
      const parent = element.parentElement;
      if (parent) {
        // 获取父元素中的所有文本节点
        const textNodes = Array.from(parent.childNodes)
          .filter(node => node.nodeType === 3 && node.textContent && node.textContent.trim());
        
        if (textNodes.length > 0) {
          return textNodes[0].textContent.trim().replace(/[:：*]$/, '');
        }
        
        // 4. 查找兄弟元素中的文本
        const siblings = Array.from(parent.children);
        for (const sibling of siblings) {
          if (sibling !== element && sibling.textContent && 
              !sibling.querySelector('input, select, textarea')) {
            const siblingText = sibling.textContent.trim().replace(/[:：*]$/, '');
            if (siblingText) return siblingText;
          }
        }
        
        // 5. 查找前面的兄弟元素（可能是标签或提示文本）
        let previousElement = element.previousElementSibling;
        while (previousElement) {
          if (previousElement.textContent && 
              !previousElement.querySelector('input, select, textarea')) {
            const prevText = previousElement.textContent.trim().replace(/[:：*]$/, '');
            if (prevText) return prevText;
          }
          previousElement = previousElement.previousElementSibling;
        }
      }
      
      // 6. 使用aria-label属性
      if (element.getAttribute('aria-label')) {
        return element.getAttribute('aria-label').trim();
      }
      
      // 7. 使用title属性
      if (element.title) {
        return element.title.trim();
      }
      
      // 8. 使用placeholder或name属性
      if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        if (element.placeholder) {
          return element.placeholder.trim();
        }
        
        if (element.name) {
          // 将name属性格式化为更可读的形式
          return element.name
            .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
            .replace(/_/g, ' ')         // 将下划线替换为空格
            .replace(/-/g, ' ')         // 将连字符替换为空格
            .trim()
            .toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase()); // 首字母大写
        }
        
        return '未命名字段';
      }
      
      // 9. 如果是select元素
      if (element instanceof HTMLSelectElement) {
        if (element.name) {
          // 将name属性格式化为更可读的形式
          return element.name
            .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
            .replace(/_/g, ' ')         // 将下划线替换为空格
            .replace(/-/g, ' ')         // 将连字符替换为空格
            .trim()
            .toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase()); // 首字母大写
        }
        return '未命名选择';
      }
      
      return '未命名字段';
    }
    
    /**
     * 从元素中提取值
     * @param {HTMLElement} element - 输入元素
     * @returns {string} 提取的值
     */
    function extractValueFromElement(element) {
      if (element instanceof HTMLSelectElement) {
        return element.options[element.selectedIndex]?.text || '';
      } else if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        return element.value || '';
      }
      return '';
    }
    
    /**
     * 处理单个表单元素
     * @param {HTMLElement} element - 表单元素
     * @param {HTMLElement} context - 上下文元素（如表单）
     */
    function processFormElement(element, context) {
      try {
        // 跳过已处理的元素
        if (processedElements.has(element)) return;
        
        // 跳过按钮、复选框和单选按钮
        if (element instanceof HTMLInputElement && 
            (element.type === 'button' || 
             element.type === 'submit' || 
             element.type === 'reset' || 
             element.type === 'checkbox' || 
             element.type === 'radio')) {
          return;
        }
        
        // 获取键名 - 先获取键名再获取值，确保即使没有值也能获取键名
        const key = extractKeyFromElement(element, context);
        
        // 获取值
        const value = extractValueFromElement(element);
        
        // 如果没有键名，生成一个默认键名
        const finalKey = key || `字段_${formData.length + 1}`;
        
        // 添加到结果中，即使值为空也添加
        formData.push({ key: finalKey, value: value });
        
        // 标记为已处理
        processedElements.add(element);
      } catch (error) {
        console.error('处理表单元素时出错:', error);
      }
    }
    
    // 创建一个数组来保存页面上所有相关的输入元素，按照它们在DOM中的顺序
    const allElements = [];
    
    // 收集所有表单元素
    try {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        const formElements = form.elements;
        for (let i = 0; i < formElements.length; i++) {
          const element = formElements[i];
          if (element instanceof HTMLInputElement || 
              element instanceof HTMLTextAreaElement || 
              element instanceof HTMLSelectElement) {
            allElements.push({ element, context: form });
          }
        }
      });
    } catch (error) {
      console.error('收集表单元素时出错:', error);
    }
    
    // 收集所有非表单输入元素
    try {
      const inputs = document.querySelectorAll('input[type="text"], input[type="number"], input[type="email"], input[type="tel"], input[type="password"], textarea, select');
      inputs.forEach(input => {
        // 检查这个元素是否已经作为表单元素被收集
        const isInForm = Array.from(document.querySelectorAll('form')).some(form => form.contains(input));
        if (!isInForm) {
          allElements.push({ element: input, context: document });
        }
      });
    } catch (error) {
      console.error('收集非表单元素时出错:', error);
    }
    
    // 按照DOM顺序排序元素
    try {
      allElements.sort((a, b) => {
        // 使用compareDocumentPosition来确定DOM中的顺序
        const position = a.element.compareDocumentPosition(b.element);
        
        // 如果b在a之前，返回正数；如果a在b之前，返回负数
        if (position & Node.DOCUMENT_POSITION_PRECEDING) {
          return 1;
        } else if (position & Node.DOCUMENT_POSITION_FOLLOWING) {
          return -1;
        }
        return 0;
      });
    } catch (error) {
      console.error('排序元素时出错:', error);
    }
    
    // 按DOM顺序处理所有元素
    allElements.forEach(item => {
      processFormElement(item.element, item.context);
    });
    
    // 如果没有找到任何元素，添加一个默认项
    if (formData.length === 0) {
      formData.push({ key: '未找到字段', value: '请手动添加数据' });
    }
    
    // 确保每一项都有key和value属性
    return formData.map((item, index) => {
      if (!item.key) {
        item.key = `字段_${index + 1}`;
      }
      if (!item.value && item.value !== '') {
        item.value = '';
      }
      return item;
    });
  } catch (error) {
    console.error('提取表单数据时出错:', error);
    return [{ key: '解析错误', value: error.message || '未知错误' }];
  }
}
</script>

<template>
  <div class="dom-capture">
    <div class="header">
      <button @click="goBack" class="back-btn">
        &larr; 返回
      </button>
      <h2>DOM解析方式</h2>
    </div>
    
    <div class="info-box">
      <p>【解析页面】→ 提取表单；检查数据完整性 → 遗漏时点击【+添加字段】；确认无误 → 【提交数据】生成修改意见</p>
    </div>
    
    <div v-if="error" class="error">
      <strong>错误：</strong> {{ error }}
    </div>
    
    <div class="controls">
      <button 
        @click="parseDom" 
        :disabled="loading"
        class="parse-btn"
      >
        <span v-if="loading" class="loading-spinner"></span>
        {{ loading ? '解析中...' : '重新解析页面' }}
      </button>
      
      <button 
        v-if="formData.length > 0"
        @click="clearStorage" 
        class="clear-btn"
        title="清除所有已保存的数据"
      >
        清除数据
      </button>
    </div>
    
    <div v-if="formData.length > 0" class="form-data-container">
      <div class="form-data-header">
        <h3>解析结果：共 {{ formData.length }} 项</h3>
        <button @click="addNewItem" class="add-btn">
          + 添加字段
        </button>
      </div>
      
      <div class="form-data-list">
        <div v-if="formData.length === 0" class="empty-state">
          未找到任何数据。您可以点击"添加字段"手动添加。
        </div>
        
        <div v-for="(item, index) in formData" :key="index" class="form-data-item">
          <div class="item-order-buttons">
            <button 
              @click="moveItemUp(index)" 
              :disabled="index === 0"
              class="order-btn up-btn"
              title="上移"
            >
              ▲
            </button>
            <button 
              @click="moveItemDown(index)" 
              :disabled="index === formData.length - 1"
              class="order-btn down-btn"
              title="下移"
            >
              ▼
            </button>
          </div>
          <div class="form-data-inputs">
            <input 
              type="text" 
              :value="item.key" 
              @input="e => editItem(index, 'key', (e.target as HTMLInputElement).value)"
              placeholder="字段名"
              class="key-input"
            />
            <span class="separator">:</span>
            <input 
              type="text" 
              :value="item.value" 
              @input="e => editItem(index, 'value', (e.target as HTMLInputElement).value)"
              placeholder="值"
              class="value-input"
            />
          </div>
          <button @click="removeItem(index)" class="remove-btn" title="删除此项">
            &times;
          </button>
        </div>
      </div>
      
      <div class="form-actions">
        <button 
          @click="submitToBackend" 
          :disabled="apiLoading || formData.length === 0"
          class="submit-btn"
        >
          <span v-if="apiLoading" class="loading-spinner"></span>
          {{ apiLoading ? '提交中...' : '提交数据' }}
        </button>
      </div>
      
      <div v-if="apiError" class="error">
        <strong>提交失败：</strong> {{ apiError }}
      </div>
      
      <div v-if="submitSuccess" class="success">
        <strong>成功！</strong> 数据已成功提交到后端。
        <div v-if="validationResponse" class="validation-response">
          <strong>修改建议：</strong>
          <div class="markdown-content" v-html="parsedValidationResponse"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dom-capture {
  padding: 1rem;
  max-width: 500px;
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.back-btn {
  background: none;
  border: none;
  color: #2196F3;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.3rem 0.5rem;
  margin-right: 1rem;
  border-radius: 4px;
}

.back-btn:hover {
  background-color: #f0f0f0;
}

h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.info-box {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  color: #0d47a1;
}

.info-box p {
  margin: 0.5rem 0;
}

.info-box p:first-child {
  margin-top: 0;
}

.info-box p:last-child {
  margin-bottom: 0;
}

.controls {
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.parse-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.parse-btn:hover {
  background-color: #45a049;
}

.parse-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  color: #f44336;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 4px solid #f44336;
}

.success {
  color: #4CAF50;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #e8f5e9;
  border-radius: 4px;
  text-align: center;
  border-left: 4px solid #4CAF50;
}

.form-data-container {
  margin-top: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  background-color: #f9f9f9;
}

.form-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.1rem;
  margin: 0;
}

.add-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  font-size: 0.9rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.form-data-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.empty-state {
  padding: 1rem;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-data-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.form-data-item:hover {
  border-color: #bbdefb;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.item-order-buttons {
  display: flex;
  flex-direction: column;
  margin-right: 0.5rem;
}

.order-btn {
  background: none;
  border: none;
  color: #757575;
  font-size: 0.7rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.order-btn:hover:not(:disabled) {
  color: #2196F3;
}

.order-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.up-btn {
  margin-bottom: 2px;
}

.form-data-inputs {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 确保flex项可以缩小到小于内容大小 */
}

.key-input, .value-input {
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  min-width: 0; /* 允许输入框缩小 */
  overflow: hidden; /* 防止溢出 */
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
}

.key-input:focus, .value-input:focus {
  border-color: #2196F3;
  outline: none;
}

.key-input {
  width: 40%;
  margin-right: 0.5rem;
  min-width: 80px; /* 确保key输入框有最小宽度 */
}

.separator {
  margin: 0 0.5rem;
  color: #666;
  flex-shrink: 0; /* 防止分隔符被压缩 */
}

.value-input {
  flex: 1;
  min-width: 80px; /* 确保value输入框有最小宽度 */
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0.5rem;
  margin-left: 0.5rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  color: #d32f2f;
  background-color: #ffebee;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.submit-btn {
  background-color: #FF9800;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover {
  background-color: #e68a00;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.validation-response {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #2196F3;
}

.validation-response :deep(p) {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.validation-response :deep(ul), 
.validation-response :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.validation-response :deep(li) {
  margin: 0.25rem 0;
}

.validation-response :deep(strong) {
  color: #1976D2;
}

.validation-response :deep(code) {
  background-color: #E3F2FD;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.validation-response :deep(blockquote) {
  margin: 0.5rem 0;
  padding-left: 1rem;
  border-left: 3px solid #90CAF9;
  color: #546E7A;
}

.markdown-content {
  font-size: 0.95rem;
  color: #333;
}

.clear-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-btn:hover {
  background-color: #d32f2f;
}
</style> 