import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
export default defineConfig({
  modules: ['@wxt-dev/module-vue'],
  manifest: ({ browser }) => ({
    name: "信息获取助手",
    description: "提供多种方式获取和解析页面信息",
    permissions: [
      "activeTab",
      "tabs",
      "windows",
      "storage",
      "scripting",
      // Chrome 需要 sidePanel 权限
      ...(browser === 'chrome' ? ["sidePanel"] : [])
    ],
    host_permissions: ["<all_urls>", "http://127.0.0.1:8000/*"],
    icons: {
      "16": "icon/16.png",
      "32": "icon/32.png",
      "48": "icon/48.png",
      "96": "icon/96.png",
      "128": "icon/128.png"
    },
    // Chrome 使用 side_panel
    ...(browser === 'chrome' && {
      side_panel: {
        default_path: "sidepanel.html"
      }
    }),
    // Firefox 使用 sidebar_action
    ...(browser === 'firefox' && {
      sidebar_action: {
        default_title: "信息获取助手",
        default_panel: "sidepanel.html",
        default_icon: {
          "16": "icon/16.png",
          "32": "icon/32.png",
          "48": "icon/48.png",
          "96": "icon/96.png",
          "128": "icon/128.png"
        }
      }
    }),
    content_security_policy: {
      extension_pages: "script-src 'self'; object-src 'self'"
    }
  })
});
