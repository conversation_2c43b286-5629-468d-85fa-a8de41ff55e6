<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>信息获取助手</title>

  <!-- Chrome side panel 配置 -->
  <meta name="manifest.open_at_install" content="true" />
  <meta name="manifest.browser_style" content="false" />

  <!-- Firefox sidebar 配置 -->
  <meta name="manifest.default_title" content="信息获取助手" />
  <meta name="manifest.default_icon" content="{
    '16': '/icon/16.png',
    '32': '/icon/32.png',
    '48': '/icon/48.png',
    '96': '/icon/96.png',
    '128': '/icon/128.png'
  }" />
</head>
<body>
  <div id="app"></div>
  <script type="module" src="./main.ts"></script>
</body>
</html>
