# 防火墙配置解析助手浏览器扩展

这是一个基于WXT框架和Vue 3开发的跨浏览器扩展，专门用于解析防火墙页面配置信息。支持Chrome、Firefox和Edge浏览器。

## 功能特点

### Strategy页面解析
- 专门针对Strategy防火墙页面进行DOM结构解析
- 自动提取防火墙规则、配置信息和状态数据
- 支持编辑、添加和删除提取的键值对
- 将解析的配置信息发送到后端进行分析

### Hillstone页面解析
- 专门针对Hillstone防火墙页面进行DOM结构解析
- 自动提取防火墙规则、配置信息和状态数据
- 支持编辑、添加和删除提取的键值对
- 将解析的配置信息发送到后端进行分析

### API配置管理
- 支持动态配置后端API服务器地址和端口
- 内置连接测试功能，确保API服务可用
- 配置信息本地持久化存储

## 安装与开发

### 开发环境要求

- [Node.js](https://nodejs.org/) (推荐 v16 或更高版本)
- [npm](https://www.npmjs.com/) 或 [pnpm](https://pnpm.io/)

### 安装依赖

```bash
npm install
# 或
pnpm install
```

### 开发模式

```bash
# Chrome浏览器 (默认)
npm run dev
# Firefox浏览器
npm run dev:firefox
```

### 构建生产版本

```bash
# Chrome浏览器 (默认)
npm run build
# Firefox浏览器
npm run build:firefox
# 构建所有浏览器版本
npm run build:all
```

### 打包扩展

```bash
# Chrome浏览器 (默认)
npm run zip
# Firefox浏览器
npm run zip:firefox
# 打包所有浏览器版本
npm run zip:all
```

## 配置后端API

### 方式一：通过界面配置（推荐）
1. 在扩展主页面点击"显示 API配置"
2. 输入服务器地址和端口（如：127.0.0.1:8000）
3. 点击"测试连接"确保服务可用
4. 点击"保存配置"完成设置

### 方式二：修改配置文件
在 `components/config.ts` 文件中修改默认配置：

```typescript
export const API_CONFIG = {
  // 后端API基础URL，默认值
  BASE_URL: 'http://127.0.0.1:8000',

  // API端点
  ENDPOINTS: {
    // 健康检查API
    HEALTH_CHECK: '/health',
    // Strategy页面解析API
    STRATEGY_PARSE: '/strategy/parse',
    // Hillstone页面解析API
    HILLSTONE_PARSE: '/hillstone/parse',
    // 表单验证API
    FORM_VALIDATE: '/form/validate'
  }
};
```

## 使用方法

1. 安装扩展后，在防火墙管理页面打开浏览器侧边栏
2. 在主页面选择对应的防火墙类型：

### Strategy页面解析
1. 在Strategy防火墙管理页面打开扩展
2. 点击"解析Strategy页面"按钮
3. 扩展会自动分析页面中的防火墙配置信息
4. 解析结果会显示在侧边栏中，可以进行编辑和调整
5. 确认数据无误后，点击"提交数据"发送到后端分析

### Hillstone页面解析
1. 在Hillstone防火墙管理页面打开扩展
2. 点击"解析Hillstone页面"按钮
3. 扩展会自动分析页面中的防火墙配置信息
4. 解析结果会显示在侧边栏中，可以进行编辑和调整
5. 确认数据无误后，点击"提交数据"发送到后端分析

### API配置
1. 在主页面点击"显示 API配置"
2. 配置后端服务器地址和端口
3. 测试连接确保服务可用

## 浏览器支持

### Chrome/Edge
- 使用 `side_panel` API 实现侧边栏
- 完整支持所有功能
- 推荐的开发和使用环境

### Firefox
- 使用 `sidebar_action` API 实现侧边栏
- 完整支持所有功能
- 自动检测并适配Firefox特有的API

## 注意事项

- 由于浏览器安全限制，某些特殊页面（如浏览器设置页、新标签页等）可能无法正常使用
- 不同浏览器的特殊页面URL格式不同，扩展会自动检测并提示
- 确保后端服务器已正确配置并运行，并实现相应的API端点
- 后端API需要支持跨域请求
- 页面解析功能需要在对应的防火墙管理页面上使用才能正常工作
- Strategy和Hillstone解析器需要根据实际页面结构进行定制化开发

## 推荐的IDE设置

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)
