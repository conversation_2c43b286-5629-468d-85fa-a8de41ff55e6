# 信息获取助手浏览器扩展

这是一个基于WXT框架和Vue 3开发的跨浏览器扩展，用于获取和解析页面信息。支持Chrome、Firefox和Edge浏览器。

## 功能特点

### 页面截图方式
- 截取当前页面的截图
- 将截图发送到后端服务器进行内容解析
- 显示解析出的键值对信息
- 支持下载截图到本地

### DOM解析方式
- 直接解析页面DOM结构，提取表单数据和输入字段
- 自动识别标签和输入框的对应关系
- 支持编辑、添加和删除键值对
- 将收集的信息发送到后端

## 安装与开发

### 开发环境要求

- [Node.js](https://nodejs.org/) (推荐 v16 或更高版本)
- [npm](https://www.npmjs.com/) 或 [pnpm](https://pnpm.io/)

### 安装依赖

```bash
npm install
# 或
pnpm install
```

### 开发模式

```bash
# Chrome浏览器 (默认)
npm run dev
# Firefox浏览器
npm run dev:firefox
```

### 构建生产版本

```bash
# Chrome浏览器 (默认)
npm run build
# Firefox浏览器
npm run build:firefox
# 构建所有浏览器版本
npm run build:all
```

### 打包扩展

```bash
# Chrome浏览器 (默认)
npm run zip
# Firefox浏览器
npm run zip:firefox
# 打包所有浏览器版本
npm run zip:all
```

## 配置后端API

在 `components/config.ts` 文件中配置后端API地址：

```typescript
export const API_CONFIG = {
  // 后端API基础URL，请根据实际环境修改
  BASE_URL: 'http://localhost:8080',
  
  // API端点
  ENDPOINTS: {
    // 图片解析API
    IMAGE_PARSE: '/image/parse',
    // 表单数据提交API
    FORM_DATA_SUBMIT: '/form/submit'
  }
};
```

## 使用方法

1. 安装扩展后，在任意网页点击扩展图标
2. 在主页面选择信息获取方式：

### 页面截图方式
1. 点击"截取当前页面"按钮
2. 截图完成后，可以选择：
   - 点击"下载截图"保存到本地
   - 点击"解析内容"将截图发送到后端进行解析
3. 解析完成后，页面会显示识别出的键值对信息

### DOM解析方式
1. 点击"解析页面DOM"按钮
2. 扩展会自动分析页面中的表单和输入字段
3. 解析结果会显示在侧边栏中，可以进行编辑
4. 点击"提交数据"将收集的信息发送到后端

## 浏览器支持

### Chrome/Edge
- 使用 `side_panel` API 实现侧边栏
- 完整支持所有功能
- 推荐的开发和使用环境

### Firefox
- 使用 `sidebar_action` API 实现侧边栏
- 完整支持所有功能
- 自动检测并适配Firefox特有的API

## 注意事项

- 由于浏览器安全限制，某些特殊页面（如浏览器设置页、新标签页等）可能无法正常使用
- 不同浏览器的特殊页面URL格式不同，扩展会自动检测并提示
- 确保后端服务器已正确配置并运行
- 后端API需要支持跨域请求
- DOM解析方式需要页面中有表单元素或输入字段才能正常工作

## 推荐的IDE设置

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)
