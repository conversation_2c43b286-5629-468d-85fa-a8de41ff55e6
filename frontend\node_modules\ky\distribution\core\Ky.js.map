{"version": 3, "file": "Ky.js", "sourceRoot": "", "sources": ["../../source/core/Ky.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,SAAS,EAAC,MAAM,wBAAwB,CAAC;AACjD,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AASvD,OAAO,EAAC,aAAa,EAAE,cAAc,EAAC,MAAM,kBAAkB,CAAC;AAC/D,OAAO,EAAC,YAAY,EAAE,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC3D,OAAO,EAAC,sBAAsB,EAAE,qBAAqB,EAAC,MAAM,uBAAuB,CAAC;AACpF,OAAO,OAA8B,MAAM,qBAAqB,CAAC;AACjE,OAAO,KAAK,MAAM,mBAAmB,CAAC;AAEtC,OAAO,EAAC,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AACvD,OAAO,EACN,cAAc,EACd,aAAa,EACb,IAAI,EACJ,uBAAuB,EACvB,gBAAgB,EAChB,uBAAuB,EACvB,sBAAsB,GACtB,MAAM,gBAAgB,CAAC;AAExB,MAAM,OAAO,EAAE;IACd,MAAM,CAAC,MAAM,CAAC,KAAY,EAAE,OAAgB;QAC3C,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAElC,MAAM,SAAS,GAAG,KAAK,IAAuB,EAAE;YAC/C,IAAI,OAAO,EAAE,CAAC,QAAQ,CAAC,OAAO,KAAK,QAAQ,IAAI,EAAE,CAAC,QAAQ,CAAC,OAAO,GAAG,cAAc,EAAE,CAAC;gBACrF,MAAM,IAAI,UAAU,CAAC,iDAAiD,cAAc,EAAE,CAAC,CAAC;YACzF,CAAC;YAED,0EAA0E;YAC1E,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,2FAA2F;YAC3F,uFAAuF;YACvF,IAAI,QAAQ,GAAG,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC;YAEjC,KAAK,MAAM,IAAI,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBACpD,4CAA4C;gBAC5C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAClC,EAAE,CAAC,OAAO,EACV,EAAE,CAAC,QAA6B,EAChC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CACtC,CAAC;gBAEF,IAAI,gBAAgB,YAAY,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACrD,QAAQ,GAAG,gBAAgB,CAAC;gBAC7B,CAAC;YACF,CAAC;YAED,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAE/B,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACjD,IAAI,KAAK,GAAG,IAAI,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,QAA6B,CAAC,CAAC;gBAElF,KAAK,MAAM,IAAI,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;oBAClD,4CAA4C;oBAC5C,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3B,CAAC;gBAED,MAAM,KAAK,CAAC;YACb,CAAC;YAED,uEAAuE;YACvE,IAAI,EAAE,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBACpC,IAAI,OAAO,EAAE,CAAC,QAAQ,CAAC,kBAAkB,KAAK,UAAU,EAAE,CAAC;oBAC1D,MAAM,IAAI,SAAS,CAAC,oDAAoD,CAAC,CAAC;gBAC3E,CAAC;gBAED,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAC9B,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;gBAChG,CAAC;gBAED,OAAO,cAAc,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YACzE,CAAC;YAED,OAAO,QAAQ,CAAC;QACjB,CAAC,CAAC;QAEF,MAAM,iBAAiB,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QAC9F,MAAM,MAAM,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;aACrE,OAAO,CAAC,KAAK,IAAI,EAAE;YACnB,0FAA0F;YAC1F,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC1B,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;YACjC,CAAC;QACF,CAAC,CAAoB,CAAC;QAEvB,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAwC,EAAE,CAAC;YACrG,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;gBACzB,wEAAwE;gBACxE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC;gBAE/E,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC;gBAE9B,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;oBACrB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;wBAC7B,OAAO,EAAE,CAAC;oBACX,CAAC;oBAED,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;oBACzD,MAAM,YAAY,GAAG,WAAW,CAAC,UAAU,CAAC;oBAC5C,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;wBACxB,OAAO,EAAE,CAAC;oBACX,CAAC;oBAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;wBACvB,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;oBACjD,CAAC;gBACF,CAAC;gBAED,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,CAAC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,OAAO,CAAU;IACd,eAAe,CAAmB;IAClC,WAAW,GAAG,CAAC,CAAC;IAChB,MAAM,CAAQ;IACd,QAAQ,CAAkB;IAEpC,sCAAsC;IACtC,YAAY,KAAY,EAAE,UAAmB,EAAE;QAC9C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,QAAQ,GAAG;YACf,GAAG,OAAO;YACV,OAAO,EAAE,YAAY,CAAE,IAAI,CAAC,MAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC;YACxE,KAAK,EAAE,UAAU,CAChB;gBACC,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,EAAE;gBACf,aAAa,EAAE,EAAE;aACjB,EACD,OAAO,CAAC,KAAK,CACb;YACD,MAAM,EAAE,sBAAsB,CAAC,OAAO,CAAC,MAAM,IAAK,IAAI,CAAC,MAAkB,CAAC,MAAM,IAAI,KAAK,CAAC;YAC1F,wEAAwE;YACxE,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;YAC1C,KAAK,EAAE,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC;YAC3C,eAAe,EAAE,OAAO,CAAC,eAAe,KAAK,KAAK;YAClD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,MAAM;YAClC,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;SACzD,CAAC;QAEF,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,MAAM,YAAY,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACnH,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAChE,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC/E,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,GAAG,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QACrD,CAAC;QAED,IAAI,uBAAuB,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAK,IAAI,CAAC,MAAkB,CAAC,MAAM,CAAC;YAC/E,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACtI,CAAC;QAED,IAAI,sBAAsB,EAAE,CAAC;YAC5B,yCAAyC;YACzC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC7G,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,kBAAkB,CAAC,CAAC;QAC5G,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAChC,yDAAyD;YACzD,MAAM,gBAAgB,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,KAAK,QAAQ;gBACtE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;gBAC/C,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,YAA2C,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC7F,yDAAyD;YACzD,MAAM,YAAY,GAAG,GAAG,GAAG,gBAAgB,CAAC;YAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;YAExE,mIAAmI;YACnI,IACC,CAAC,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,YAAY,UAAU,CAAC,QAAQ,CAAC;mBACpE,IAAI,CAAC,QAAQ,CAAC,IAAI,YAAY,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAK,IAAI,CAAC,QAAQ,CAAC,OAAkC,CAAC,cAAc,CAAC,CAAC,EAClJ,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC7C,CAAC;YAED,kHAAkH;YAClH,IAAI,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,EAAC,GAAG,IAAI,CAAC,OAAO,EAAC,CAAC,EAAE,IAAI,CAAC,QAAuB,CAAC,CAAC;QACrH,CAAC;QAED,qEAAqE;QACrE,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YACpC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,KAAK,UAAU,EAAE,CAAC;gBAC1D,MAAM,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,4GAA4G,CAAC,CAAC;YAC/H,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACvC,IAAI,YAAY,EAAE,CAAC;gBAClB,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC5E,CAAC;QACF,CAAC;IACF,CAAC;IAES,oBAAoB,CAAC,KAAc;QAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,YAAY,YAAY,EAAE,CAAC;YACnF,MAAM,KAAK,CAAC;QACb,CAAC;QAED,IAAI,KAAK,YAAY,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtE,MAAM,KAAK,CAAC;YACb,CAAC;YAED,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;mBACxD,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;mBAC7C,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC,SAAS;mBACzD,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,UAAU;YAChE,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxF,IAAI,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBACtC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7C,CAAC;qBAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC9C,yFAAyF;oBACzF,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACrB,CAAC;gBAED,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC;gBACvD,OAAO,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YAClC,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACb,CAAC;QACF,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;IAES,iBAAiB,CAAC,QAAkB;QAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC7B,QAAQ,CAAC,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAU,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,QAAQ,CAAC;IACjB,CAAC;IAES,KAAK,CAAC,MAAM,CAAiD,SAAY;QAClF,IAAI,CAAC;YACJ,OAAO,MAAM,SAAS,EAAE,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,cAAc,CAAC,CAAC;YACtE,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,KAAK,CAAC;YACb,CAAC;YAED,MAAM,KAAK,CAAC,EAAE,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,CAAC,CAAC;YAEhD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBACpD,4CAA4C;gBAC5C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC;oBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,OAAO,EAAG,IAAI,CAAC,QAAyC;oBACxD,KAAK,EAAE,KAAc;oBACrB,UAAU,EAAE,IAAI,CAAC,WAAW;iBAC5B,CAAC,CAAC;gBAEH,oEAAoE;gBACpE,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;oBACzB,OAAO;gBACR,CAAC;YACF,CAAC;YAED,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;IACF,CAAC;IAES,KAAK,CAAC,MAAM;QACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACtD,4CAA4C;YAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAG,IAAI,CAAC,QAAyC,CAAC,CAAC;YAEzF,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;gBAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;gBACtB,MAAM;YACP,CAAC;YAED,IAAI,MAAM,YAAY,QAAQ,EAAE,CAAC;gBAChC,OAAO,MAAM,CAAC;YACf,CAAC;QACF,CAAC;QAED,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1E,yDAAyD;QACzD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,OAAO,CAAC,WAAW,EAAE,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,QAA0B,CAAC,CAAC;IACvG,CAAC;CACD", "sourcesContent": ["import {HTTPError} from '../errors/HTTPError.js';\nimport {TimeoutError} from '../errors/TimeoutError.js';\nimport type {\n\tInput,\n\tInternalOptions,\n\tNormalizedOptions,\n\tOptions,\n\tSearchParamsInit,\n} from '../types/options.js';\nimport {type ResponsePromise} from '../types/ResponsePromise.js';\nimport {streamRequest, streamResponse} from '../utils/body.js';\nimport {mergeHeaders, mergeHooks} from '../utils/merge.js';\nimport {normalizeRequestMethod, normalizeRetryOptions} from '../utils/normalize.js';\nimport timeout, {type TimeoutOptions} from '../utils/timeout.js';\nimport delay from '../utils/delay.js';\nimport {type ObjectEntries} from '../utils/types.js';\nimport {findUnknownOptions} from '../utils/options.js';\nimport {\n\tmaxSafeTimeout,\n\tresponseTypes,\n\tstop,\n\tsupports<PERSON>bort<PERSON>ontroller,\n\tsupportsFormData,\n\tsupportsResponseStreams,\n\tsupportsRequestStreams,\n} from './constants.js';\n\nexport class Ky {\n\tstatic create(input: Input, options: Options): ResponsePromise {\n\t\tconst ky = new Ky(input, options);\n\n\t\tconst function_ = async (): Promise<Response> => {\n\t\t\tif (typeof ky._options.timeout === 'number' && ky._options.timeout > maxSafeTimeout) {\n\t\t\t\tthrow new RangeError(`The \\`timeout\\` option cannot be greater than ${maxSafeTimeout}`);\n\t\t\t}\n\n\t\t\t// Delay the fetch so that body method shortcuts can set the Accept header\n\t\t\tawait Promise.resolve();\n\t\t\t// Before using ky.request, _fetch clones it and saves the clone for future retries to use.\n\t\t\t// If retry is not needed, close the cloned request's ReadableStream for memory safety.\n\t\t\tlet response = await ky._fetch();\n\n\t\t\tfor (const hook of ky._options.hooks.afterResponse) {\n\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\tconst modifiedResponse = await hook(\n\t\t\t\t\tky.request,\n\t\t\t\t\tky._options as NormalizedOptions,\n\t\t\t\t\tky._decorateResponse(response.clone()),\n\t\t\t\t);\n\n\t\t\t\tif (modifiedResponse instanceof globalThis.Response) {\n\t\t\t\t\tresponse = modifiedResponse;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tky._decorateResponse(response);\n\n\t\t\tif (!response.ok && ky._options.throwHttpErrors) {\n\t\t\t\tlet error = new HTTPError(response, ky.request, ky._options as NormalizedOptions);\n\n\t\t\t\tfor (const hook of ky._options.hooks.beforeError) {\n\t\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\t\terror = await hook(error);\n\t\t\t\t}\n\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\t// If `onDownloadProgress` is passed, it uses the stream API internally\n\t\t\tif (ky._options.onDownloadProgress) {\n\t\t\t\tif (typeof ky._options.onDownloadProgress !== 'function') {\n\t\t\t\t\tthrow new TypeError('The `onDownloadProgress` option must be a function');\n\t\t\t\t}\n\n\t\t\t\tif (!supportsResponseStreams) {\n\t\t\t\t\tthrow new Error('Streams are not supported in your environment. `ReadableStream` is missing.');\n\t\t\t\t}\n\n\t\t\t\treturn streamResponse(response.clone(), ky._options.onDownloadProgress);\n\t\t\t}\n\n\t\t\treturn response;\n\t\t};\n\n\t\tconst isRetriableMethod = ky._options.retry.methods.includes(ky.request.method.toLowerCase());\n\t\tconst result = (isRetriableMethod ? ky._retry(function_) : function_())\n\t\t\t.finally(async () => {\n\t\t\t\t// Now that we know a retry is not needed, close the ReadableStream of the cloned request.\n\t\t\t\tif (!ky.request.bodyUsed) {\n\t\t\t\t\tawait ky.request.body?.cancel();\n\t\t\t\t}\n\t\t\t}) as ResponsePromise;\n\n\t\tfor (const [type, mimeType] of Object.entries(responseTypes) as ObjectEntries<typeof responseTypes>) {\n\t\t\tresult[type] = async () => {\n\t\t\t\t// eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n\t\t\t\tky.request.headers.set('accept', ky.request.headers.get('accept') || mimeType);\n\n\t\t\t\tconst response = await result;\n\n\t\t\t\tif (type === 'json') {\n\t\t\t\t\tif (response.status === 204) {\n\t\t\t\t\t\treturn '';\n\t\t\t\t\t}\n\n\t\t\t\t\tconst arrayBuffer = await response.clone().arrayBuffer();\n\t\t\t\t\tconst responseSize = arrayBuffer.byteLength;\n\t\t\t\t\tif (responseSize === 0) {\n\t\t\t\t\t\treturn '';\n\t\t\t\t\t}\n\n\t\t\t\t\tif (options.parseJson) {\n\t\t\t\t\t\treturn options.parseJson(await response.text());\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn response[type]();\n\t\t\t};\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tpublic request: Request;\n\tprotected abortController?: AbortController;\n\tprotected _retryCount = 0;\n\tprotected _input: Input;\n\tprotected _options: InternalOptions;\n\n\t// eslint-disable-next-line complexity\n\tconstructor(input: Input, options: Options = {}) {\n\t\tthis._input = input;\n\n\t\tthis._options = {\n\t\t\t...options,\n\t\t\theaders: mergeHeaders((this._input as Request).headers, options.headers),\n\t\t\thooks: mergeHooks(\n\t\t\t\t{\n\t\t\t\t\tbeforeRequest: [],\n\t\t\t\t\tbeforeRetry: [],\n\t\t\t\t\tbeforeError: [],\n\t\t\t\t\tafterResponse: [],\n\t\t\t\t},\n\t\t\t\toptions.hooks,\n\t\t\t),\n\t\t\tmethod: normalizeRequestMethod(options.method ?? (this._input as Request).method ?? 'GET'),\n\t\t\t// eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n\t\t\tprefixUrl: String(options.prefixUrl || ''),\n\t\t\tretry: normalizeRetryOptions(options.retry),\n\t\t\tthrowHttpErrors: options.throwHttpErrors !== false,\n\t\t\ttimeout: options.timeout ?? 10_000,\n\t\t\tfetch: options.fetch ?? globalThis.fetch.bind(globalThis),\n\t\t};\n\n\t\tif (typeof this._input !== 'string' && !(this._input instanceof URL || this._input instanceof globalThis.Request)) {\n\t\t\tthrow new TypeError('`input` must be a string, URL, or Request');\n\t\t}\n\n\t\tif (this._options.prefixUrl && typeof this._input === 'string') {\n\t\t\tif (this._input.startsWith('/')) {\n\t\t\t\tthrow new Error('`input` must not begin with a slash when using `prefixUrl`');\n\t\t\t}\n\n\t\t\tif (!this._options.prefixUrl.endsWith('/')) {\n\t\t\t\tthis._options.prefixUrl += '/';\n\t\t\t}\n\n\t\t\tthis._input = this._options.prefixUrl + this._input;\n\t\t}\n\n\t\tif (supportsAbortController) {\n\t\t\tconst originalSignal = this._options.signal ?? (this._input as Request).signal;\n\t\t\tthis.abortController = new globalThis.AbortController();\n\t\t\tthis._options.signal = originalSignal ? AbortSignal.any([originalSignal, this.abortController.signal]) : this.abortController.signal;\n\t\t}\n\n\t\tif (supportsRequestStreams) {\n\t\t\t// @ts-expect-error - Types are outdated.\n\t\t\tthis._options.duplex = 'half';\n\t\t}\n\n\t\tif (this._options.json !== undefined) {\n\t\t\tthis._options.body = this._options.stringifyJson?.(this._options.json) ?? JSON.stringify(this._options.json);\n\t\t\tthis._options.headers.set('content-type', this._options.headers.get('content-type') ?? 'application/json');\n\t\t}\n\n\t\tthis.request = new globalThis.Request(this._input, this._options);\n\n\t\tif (this._options.searchParams) {\n\t\t\t// eslint-disable-next-line unicorn/prevent-abbreviations\n\t\t\tconst textSearchParams = typeof this._options.searchParams === 'string'\n\t\t\t\t? this._options.searchParams.replace(/^\\?/, '')\n\t\t\t\t: new URLSearchParams(this._options.searchParams as unknown as SearchParamsInit).toString();\n\t\t\t// eslint-disable-next-line unicorn/prevent-abbreviations\n\t\t\tconst searchParams = '?' + textSearchParams;\n\t\t\tconst url = this.request.url.replace(/(?:\\?.*?)?(?=#|$)/, searchParams);\n\n\t\t\t// To provide correct form boundary, Content-Type header should be deleted each time when new Request instantiated from another one\n\t\t\tif (\n\t\t\t\t((supportsFormData && this._options.body instanceof globalThis.FormData)\n\t\t\t\t\t|| this._options.body instanceof URLSearchParams) && !(this._options.headers && (this._options.headers as Record<string, string>)['content-type'])\n\t\t\t) {\n\t\t\t\tthis.request.headers.delete('content-type');\n\t\t\t}\n\n\t\t\t// The spread of `this.request` is required as otherwise it misses the `duplex` option for some reason and throws.\n\t\t\tthis.request = new globalThis.Request(new globalThis.Request(url, {...this.request}), this._options as RequestInit);\n\t\t}\n\n\t\t// If `onUploadProgress` is passed, it uses the stream API internally\n\t\tif (this._options.onUploadProgress) {\n\t\t\tif (typeof this._options.onUploadProgress !== 'function') {\n\t\t\t\tthrow new TypeError('The `onUploadProgress` option must be a function');\n\t\t\t}\n\n\t\t\tif (!supportsRequestStreams) {\n\t\t\t\tthrow new Error('Request streams are not supported in your environment. The `duplex` option for `Request` is not available.');\n\t\t\t}\n\n\t\t\tconst originalBody = this.request.body;\n\t\t\tif (originalBody) {\n\t\t\t\tthis.request = streamRequest(this.request, this._options.onUploadProgress);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _calculateRetryDelay(error: unknown) {\n\t\tthis._retryCount++;\n\n\t\tif (this._retryCount > this._options.retry.limit || error instanceof TimeoutError) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (error instanceof HTTPError) {\n\t\t\tif (!this._options.retry.statusCodes.includes(error.response.status)) {\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tconst retryAfter = error.response.headers.get('Retry-After')\n\t\t\t\t?? error.response.headers.get('RateLimit-Reset')\n\t\t\t\t?? error.response.headers.get('X-RateLimit-Reset') // GitHub\n\t\t\t\t?? error.response.headers.get('X-Rate-Limit-Reset'); // Twitter\n\t\t\tif (retryAfter && this._options.retry.afterStatusCodes.includes(error.response.status)) {\n\t\t\t\tlet after = Number(retryAfter) * 1000;\n\t\t\t\tif (Number.isNaN(after)) {\n\t\t\t\t\tafter = Date.parse(retryAfter) - Date.now();\n\t\t\t\t} else if (after >= Date.parse('2024-01-01')) {\n\t\t\t\t\t// A large number is treated as a timestamp (fixed threshold protects against clock skew)\n\t\t\t\t\tafter -= Date.now();\n\t\t\t\t}\n\n\t\t\t\tconst max = this._options.retry.maxRetryAfter ?? after;\n\t\t\t\treturn after < max ? after : max;\n\t\t\t}\n\n\t\t\tif (error.response.status === 413) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\n\t\tconst retryDelay = this._options.retry.delay(this._retryCount);\n\t\treturn Math.min(this._options.retry.backoffLimit, retryDelay);\n\t}\n\n\tprotected _decorateResponse(response: Response): Response {\n\t\tif (this._options.parseJson) {\n\t\t\tresponse.json = async () => this._options.parseJson!(await response.text());\n\t\t}\n\n\t\treturn response;\n\t}\n\n\tprotected async _retry<T extends (...arguments_: any) => Promise<any>>(function_: T): Promise<ReturnType<T> | void> {\n\t\ttry {\n\t\t\treturn await function_();\n\t\t} catch (error) {\n\t\t\tconst ms = Math.min(this._calculateRetryDelay(error), maxSafeTimeout);\n\t\t\tif (this._retryCount < 1) {\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\tawait delay(ms, {signal: this._options.signal});\n\n\t\t\tfor (const hook of this._options.hooks.beforeRetry) {\n\t\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\t\tconst hookResult = await hook({\n\t\t\t\t\trequest: this.request,\n\t\t\t\t\toptions: (this._options as unknown) as NormalizedOptions,\n\t\t\t\t\terror: error as Error,\n\t\t\t\t\tretryCount: this._retryCount,\n\t\t\t\t});\n\n\t\t\t\t// If `stop` is returned from the hook, the retry process is stopped\n\t\t\t\tif (hookResult === stop) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this._retry(function_);\n\t\t}\n\t}\n\n\tprotected async _fetch(): Promise<Response> {\n\t\tfor (const hook of this._options.hooks.beforeRequest) {\n\t\t\t// eslint-disable-next-line no-await-in-loop\n\t\t\tconst result = await hook(this.request, (this._options as unknown) as NormalizedOptions);\n\n\t\t\tif (result instanceof Request) {\n\t\t\t\tthis.request = result;\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (result instanceof Response) {\n\t\t\t\treturn result;\n\t\t\t}\n\t\t}\n\n\t\tconst nonRequestOptions = findUnknownOptions(this.request, this._options);\n\n\t\t// Cloning is done here to prepare in advance for retries\n\t\tconst mainRequest = this.request;\n\t\tthis.request = mainRequest.clone();\n\n\t\tif (this._options.timeout === false) {\n\t\t\treturn this._options.fetch(mainRequest, nonRequestOptions);\n\t\t}\n\n\t\treturn timeout(mainRequest, nonRequestOptions, this.abortController, this._options as TimeoutOptions);\n\t}\n}\n"]}