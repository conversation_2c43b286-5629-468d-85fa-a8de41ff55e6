{"name": "run-applescript", "version": "5.0.0", "description": "Run AppleScript and get the result", "license": "MIT", "repository": "sindresorhus/run-applescript", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["macos", "mac", "applescript", "osascript", "run", "execute"], "dependencies": {"execa": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}