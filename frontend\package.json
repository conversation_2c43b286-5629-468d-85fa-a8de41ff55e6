{"name": "wxt-vue-starter", "description": "信息获取助手 - 提供多种方式获取和解析页面信息", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare", "build:all": "npm run build && npm run build:firefox", "zip:all": "npm run zip && npm run zip:firefox"}, "dependencies": {"marked": "^15.0.12", "vue": "^3.5.13"}, "devDependencies": {"@wxt-dev/module-vue": "^1.0.2", "typescript": "5.6.3", "vue-tsc": "^2.2.10", "wxt": "^0.20.6"}}