# 业务改造变更日志

## 主要改动概述

本次改造将原有的通用信息获取助手转换为专门的防火墙配置解析助手，主要变更如下：

### 1. 功能重构

#### 移除功能
- ❌ **ScreenCapture.vue** - 页面截图OCR方式已隐藏
- ❌ **DomCapture.vue** - 通用DOM解析方式已隐藏

#### 新增功能
- ✅ **Strategy.vue** - Strategy防火墙页面专用解析器
- ✅ **Hillstone.vue** - Hillstone防火墙页面专用解析器
- ✅ **API配置管理** - 动态配置后端服务器地址和端口
- ✅ **连接测试** - 验证API服务器可用性

### 2. 文件变更详情

#### 新增文件
```
frontend/components/Strategy.vue      - Strategy防火墙页面解析器
frontend/components/Hillstone.vue    - Hillstone防火墙页面解析器
frontend/components/browserUtils.ts  - 浏览器检测工具（Firefox支持）
frontend/CHANGELOG.md               - 本变更日志
```

#### 修改文件
```
frontend/components/HomePage.vue     - 添加API配置界面，更新导航选项
frontend/components/config.ts       - 重构为动态配置，添加连接测试
frontend/entrypoints/sidepanel/App.vue - 更新路由，支持新组件
frontend/package.json              - 更新项目名称和描述
frontend/wxt.config.ts             - 更新扩展名称和描述
frontend/entrypoints/sidepanel/index.html - 更新页面标题
frontend/README.md                 - 更新文档说明
```

#### 隐藏文件
```
frontend/components/ScreenCapture.vue - 添加注释说明已隐藏
frontend/components/DomCapture.vue   - 添加注释说明已隐藏
```

### 3. API端点变更

#### 原有端点（已移除）
```
/image/parse      - 图片解析API
/form/submit      - 表单数据提交API
```

#### 新增端点
```
/health           - 健康检查API
/strategy/parse   - Strategy页面解析API
/hillstone/parse  - Hillstone页面解析API
/form/validate    - 表单验证API（保留）
```

### 4. 配置管理

#### 动态配置功能
- 用户可在界面中配置API服务器地址和端口
- 配置信息自动保存到localStorage
- 提供连接测试功能验证服务可用性

#### 默认配置
```typescript
BASE_URL: 'http://127.0.0.1:8000'
```

### 5. 浏览器支持

#### 跨浏览器兼容
- ✅ Chrome - 使用side_panel API
- ✅ Firefox - 使用sidebar_action API  
- ✅ Edge - 兼容Chrome API

#### 浏览器检测
- 自动检测当前浏览器类型
- 显示浏览器标识徽章
- 针对不同浏览器优化API调用

### 6. 数据存储

#### localStorage键名规范
```
Strategy页面: 
- strategy_form_data
- strategy_validation_response  
- strategy_submit_success

Hillstone页面:
- hillstone_form_data
- hillstone_validation_response
- hillstone_submit_success

API配置:
- api_config_base_url
```

### 7. 样式主题

#### Strategy页面
- 主色调: 绿色系 (#4CAF50)
- 图标: 🛡️

#### Hillstone页面  
- 主色调: 橙色系 (#FF9800)
- 图标: 🔥

### 8. 待完成工作

#### Strategy.vue
- [ ] 根据实际Strategy页面结构实现`extractStrategyData()`函数
- [ ] 定制化数据提取逻辑

#### Hillstone.vue
- [ ] 根据实际Hillstone页面结构实现`extractHillstoneData()`函数  
- [ ] 定制化数据提取逻辑

#### 后端API
- [ ] 实现`/health`健康检查端点
- [ ] 实现`/strategy/parse`解析端点
- [ ] 实现`/hillstone/parse`解析端点

### 9. 使用说明

#### 开发模式
```bash
npm run dev          # Chrome开发模式
npm run dev:firefox  # Firefox开发模式
```

#### 构建部署
```bash
npm run build:all    # 构建所有浏览器版本
npm run zip:all      # 打包所有浏览器版本
```

#### API配置
1. 在扩展主页点击"显示 API配置"
2. 输入服务器地址和端口
3. 点击"测试连接"验证
4. 点击"保存配置"完成设置

### 10. 注意事项

- 原有的截图OCR功能已完全隐藏，如需恢复请手动修改App.vue
- Strategy和Hillstone解析器目前只有框架代码，需要根据实际页面结构进行开发
- 确保后端API服务器支持跨域请求
- 建议在对应的防火墙管理页面上测试解析功能
