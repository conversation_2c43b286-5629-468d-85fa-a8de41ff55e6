{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../../source/types/hooks.ts"], "names": [], "mappings": "", "sourcesContent": ["import {type stop} from '../core/constants.js';\nimport type {KyRequest, KyResponse, HTTPError} from '../index.js';\nimport type {NormalizedOptions} from './options.js';\n\nexport type BeforeRequestHook = (\n\trequest: KyRequest,\n\toptions: NormalizedOptions\n) => Request | Response | void | Promise<Request | Response | void>;\n\nexport type BeforeRetryState = {\n\trequest: KyRequest;\n\toptions: NormalizedOptions;\n\terror: Error;\n\tretryCount: number;\n};\nexport type BeforeRetryHook = (options: BeforeRetryState) => typeof stop | void | Promise<typeof stop | void>;\n\nexport type AfterResponseHook = (\n\trequest: KyRequest,\n\toptions: NormalizedOptions,\n\tresponse: KyResponse\n) => Response | void | Promise<Response | void>;\n\nexport type BeforeErrorHook = (error: HTTPError) => HTTPError | Promise<HTTPError>;\n\nexport type Hooks = {\n\t/**\n\tThis hook enables you to modify the request right before it is sent. <PERSON>y will make no further changes to the request after this. The hook function receives normalized input and options as arguments. You could, for example, modify `options.headers` here.\n\n\tA [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response) can be returned from this hook to completely avoid making a HTTP request. This can be used to mock a request, check an internal cache, etc. An **important** consideration when returning a `Response` from this hook is that all the following hooks will be skipped, so **ensure you only return a `Response` from the last hook**.\n\n\t@default []\n\t*/\n\tbeforeRequest?: BeforeRequestHook[];\n\n\t/**\n\tThis hook enables you to modify the request right before retry. Ky will make no further changes to the request after this. The hook function receives an object with the normalized request and options, an error instance, and the retry count. You could, for example, modify `request.headers` here.\n\n\tIf the request received a response, the error will be of type `HTTPError` and the `Response` object will be available at `error.response`. Be aware that some types of errors, such as network errors, inherently mean that a response was not received. In that case, the error will not be an instance of `HTTPError`.\n\n\tYou can prevent Ky from retrying the request by throwing an error. Ky will not handle it in any way and the error will be propagated to the request initiator. The rest of the `beforeRetry` hooks will not be called in this case. Alternatively, you can return the [`ky.stop`](#ky.stop) symbol to do the same thing but without propagating an error (this has some limitations, see `ky.stop` docs for details).\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst response = await ky('https://example.com', {\n\t\thooks: {\n\t\t\tbeforeRetry: [\n\t\t\t\tasync ({request, options, error, retryCount}) => {\n\t\t\t\t\tconst token = await ky('https://example.com/refresh-token');\n\t\t\t\t\toptions.headers.set('Authorization', `token ${token}`);\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t});\n\t```\n\n\t@default []\n\t*/\n\tbeforeRetry?: BeforeRetryHook[];\n\n\t/**\n\tThis hook enables you to read and optionally modify the response. The hook function receives normalized input, options, and a clone of the response as arguments. The return value of the hook function will be used by Ky as the response object if it's an instance of [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response).\n\n\t@default []\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst response = await ky('https://example.com', {\n\t\thooks: {\n\t\t\tafterResponse: [\n\t\t\t\t(_input, _options, response) => {\n\t\t\t\t\t// You could do something with the response, for example, logging.\n\t\t\t\t\tlog(response);\n\n\t\t\t\t\t// Or return a `Response` instance to overwrite the response.\n\t\t\t\t\treturn new Response('A different response', {status: 200});\n\t\t\t\t},\n\n\t\t\t\t// Or retry with a fresh token on a 403 error\n\t\t\t\tasync (input, options, response) => {\n\t\t\t\t\tif (response.status === 403) {\n\t\t\t\t\t\t// Get a fresh token\n\t\t\t\t\t\tconst token = await ky('https://example.com/token').text();\n\n\t\t\t\t\t\t// Retry with the token\n\t\t\t\t\t\toptions.headers.set('Authorization', `token ${token}`);\n\n\t\t\t\t\t\treturn ky(input, options);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t});\n\t```\n\t*/\n\tafterResponse?: AfterResponseHook[];\n\n\t/**\n\tThis hook enables you to modify the `HTTPError` right before it is thrown. The hook function receives a `HTTPError` as an argument and should return an instance of `HTTPError`.\n\n\t@default []\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tawait ky('https://example.com', {\n\t\thooks: {\n\t\t\tbeforeError: [\n\t\t\t\terror => {\n\t\t\t\t\tconst {response} = error;\n\t\t\t\t\tif (response && response.body) {\n\t\t\t\t\t\terror.name = 'GitHubError';\n\t\t\t\t\t\terror.message = `${response.body.message} (${response.status})`;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn error;\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t});\n\t```\n\t*/\n\tbeforeError?: BeforeErrorHook[];\n};\n"]}