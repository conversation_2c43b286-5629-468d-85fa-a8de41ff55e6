<!DOCTYPE html>
<!-- saved from url=(0051)http://*************:8075/YCXXYWXT/jrsqAction/fhqcl -->
<html lang="zh"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
	
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="keywords" content="">
	<meta name="description" content="">
	<title>防火墙策略</title>
	<link href="./防火墙策略_files/bootstrap.min.css" rel="stylesheet">
	<link href="./防火墙策略_files/font-awesome.min.css" rel="stylesheet">
	<!-- bootstrap-table 表格插件样式 -->
	<link href="./防火墙策略_files/bootstrap-table.min.css" rel="stylesheet">
	<link href="./防火墙策略_files/animate.min.css" rel="stylesheet">
	<link href="./防火墙策略_files/style.min.css" rel="stylesheet">
	<link href="./防火墙策略_files/ry-ui.css" rel="stylesheet">

    
    <link href="./防火墙策略_files/bootstrap-datetimepicker.min.css" rel="stylesheet">

<link rel="stylesheet" href="./防火墙策略_files/layer.css" id="layuicss-layer"><link rel="stylesheet" href="./防火墙策略_files/style.css" id="layuicss-thememoonstylecss"><link id="layuicss-laydate" rel="stylesheet" href="./防火墙策略_files/laydate.css" media="all"></head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>申请日期：</label>
                            <input type="text" class="time-input" style="width:180px " placeholder="起始时间" name="sqrq_s" lay-key="3">
                            <span>至</span>
                            <input type="text" class="time-input" style="width:180px " placeholder="结束时间" name="sqrq_e" lay-key="4">
                        </li>
                        <li>
                            <label>流程状态：</label>
                            <select name="zt">
                                <option value="">请选择</option>
                                <option value="19">完成</option>
                                <option value="1">待审批</option>
                                <option value="2">驳回/退回</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        
        <div class="col-sm-12 select-table ">
            <div class="bootstrap-table bootstrap3">
      <div class="fixed-table-toolbar"><div class="bs-bars pull-left"><div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="add(1200,800)">
                <i class="fa fa-plus"></i>申请添加
            </a>
            <a class="btn btn-primary" onclick="ckxq()">
                <i class="fa fa-eye"></i> 查看
            </a>
            <a class="btn btn-success" onclick="deleteRow()">
                <i class="fa fa-plus"></i> 删除
            </a>
        </div></div><div class="columns columns-right btn-group pull-right"><button class="btn btn-default btn-outline" type="button" name="refresh" aria-label="刷新" title="刷新"><i class="glyphicon glyphicon-refresh icon-refresh"></i> </button><div class="keep-open btn-group" title="列">
            <button class="btn btn-default btn-outline dropdown-toggle" type="button" data-toggle="dropdown" aria-label="Columns" title="列">
            <i class="glyphicon glyphicon-th icon-th"></i>
            
            <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu"><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="ID" value="1"> <span>ID</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="SQR" value="2" checked="checked"> <span>申请人</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="SQSJ" value="3" checked="checked"> <span>申请时间</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="MDHJ" value="4" checked="checked"> <span>目的业务运行环境</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="YLX" value="5" checked="checked"> <span>源类型</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="ZT" value="6"> <span>状态</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="ZT_MC" value="7" checked="checked"> <span>流程状态</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="lcrz" value="8" checked="checked"> <span>流程日志</span></label></li><li class="dropdown-item-marker" role="menuitem"><label><input type="checkbox" data-field="CLR" value="9" checked="checked"> <span>下一步处理人</span></label></li></ul></div></div></div>
      
      <div class="fixed-table-container fixed-height" style="height: 1020.69px; padding-bottom: 35px;">
      <div class="fixed-table-header" style="margin-right: 0px;"><table class="table table-bordered table-hover" style="width: 1661.33px;"><thead class="" style=""><tr><th class="bs-checkbox " style="width: 36px; " data-field="0"><div class="th-inner "><label><input name="btSelectAll" type="checkbox"><span></span></label></div><div class="fht-cell" style="width: 50.6979px;"></div></th><th style="text-align: center; width: 150px; " data-field="SQR"><div class="th-inner ">申请人</div><div class="fht-cell" style="width: 214.438px;"></div></th><th style="text-align: center; width: 180px; " data-field="SQSJ"><div class="th-inner ">申请时间</div><div class="fht-cell" style="width: 257.521px;"></div></th><th style="text-align: center; width: 180px; " data-field="MDHJ"><div class="th-inner ">目的业务运行环境</div><div class="fht-cell" style="width: 257.521px;"></div></th><th style="text-align: center; width: 180px; " data-field="YLX"><div class="th-inner ">源类型</div><div class="fht-cell" style="width: 257.521px;"></div></th><th style="text-align: center; width: 150px; " data-field="ZT_MC"><div class="th-inner ">流程状态</div><div class="fht-cell" style="width: 214.438px;"></div></th><th style="text-align: center; width: 100px; " data-field="lcrz"><div class="th-inner ">流程日志</div><div class="fht-cell" style="width: 142.625px;"></div></th><th style="text-align: center; width: 180px; " data-field="CLR"><div class="th-inner ">下一步处理人</div><div class="fht-cell" style="width: 257.573px;"></div></th></tr></thead></table></div>
      <div class="fixed-table-body">
      <div class="fixed-table-loading table table-bordered table-hover fixed-table-border" style="top: 35px; width: 1661.33px;">
      <span class="loading-wrap">
      <span class="loading-text" style="font-size: 13px;">正在努力地加载数据中，请稍候</span>
      <span class="animation-wrap"><span class="animation-dot"></span></span>
      </span>
    
      </div>
      <table id="bootstrap-table" style="word-break: break-all; overflow-wrap: break-word; table-layout: fixed; margin-top: -34px;" class="table table-bordered table-hover"><thead class="" style=""><tr><th class="bs-checkbox " style="width: 36px; " data-field="0"><div class="th-inner "><label><input name="btSelectAll" type="checkbox"><span></span></label></div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="SQR"><div class="th-inner ">申请人</div><div class="fht-cell"></div></th><th style="text-align: center; width: 180px; " data-field="SQSJ"><div class="th-inner ">申请时间</div><div class="fht-cell"></div></th><th style="text-align: center; width: 180px; " data-field="MDHJ"><div class="th-inner ">目的业务运行环境</div><div class="fht-cell"></div></th><th style="text-align: center; width: 180px; " data-field="YLX"><div class="th-inner ">源类型</div><div class="fht-cell"></div></th><th style="text-align: center; width: 150px; " data-field="ZT_MC"><div class="th-inner ">流程状态</div><div class="fht-cell"></div></th><th style="text-align: center; width: 100px; " data-field="lcrz"><div class="th-inner ">流程日志</div><div class="fht-cell"></div></th><th style="text-align: center; width: 180px; " data-field="CLR"><div class="th-inner ">下一步处理人</div><div class="fht-cell"></div></th></tr></thead><tbody><tr data-index="0" class=""><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="0" name="btSelectItem" type="checkbox" checked="checked">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">吴怡</td><td style="text-align: center; width: 180px; ">2025-07-17</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252903)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="1" class=""><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="1" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">陈伟</td><td style="text-align: center; width: 180px; ">2025-07-07</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252841)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="2" class="selected"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="2" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">陈伟</td><td style="text-align: center; width: 180px; ">2025-07-07</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252840)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="3"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="3" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">胥鸣</td><td style="text-align: center; width: 180px; ">2025-07-03</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252824)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="4"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="4" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">姜卫东</td><td style="text-align: center; width: 180px; ">2025-07-02</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252807)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="5"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="5" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">丛方舟</td><td style="text-align: center; width: 180px; ">2025-06-05</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252626)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="6"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="6" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">陈伟</td><td style="text-align: center; width: 180px; ">2025-05-29</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252587)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="7"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="7" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">吴玉冰</td><td style="text-align: center; width: 180px; ">2025-05-27</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252569)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="8"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="8" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">吴静静</td><td style="text-align: center; width: 180px; ">2025-05-20</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252501)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="9"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="9" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">蔡志林</td><td style="text-align: center; width: 180px; ">2025-05-16</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252480)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="10"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="10" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">蔡志林</td><td style="text-align: center; width: 180px; ">2025-05-16</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">信通管理员审核退回</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252479)">查看</a></td><td style="text-align: center; width: 180px; ">蔡志林</td></tr><tr data-index="11"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="11" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">蔡志林</td><td style="text-align: center; width: 180px; ">2025-05-16</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">信通管理员审核退回</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252478)">查看</a></td><td style="text-align: center; width: 180px; ">蔡志林</td></tr><tr data-index="12"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="12" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">蔡志林</td><td style="text-align: center; width: 180px; ">2025-05-16</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">服务器</td><td style="text-align: center; width: 150px; ">信通管理员审核退回</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252477)">查看</a></td><td style="text-align: center; width: 180px; ">蔡志林</td></tr><tr data-index="13"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="13" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">葛冰</td><td style="text-align: center; width: 180px; ">2025-04-30</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252384)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr><tr data-index="14"><td class="bs-checkbox " style="width: 36px; "><label>
            <input data-index="14" name="btSelectItem" type="checkbox">
            <span></span>
            </label></td><td style="text-align: center; width: 150px; ">葛冰</td><td style="text-align: center; width: 180px; ">2025-04-29</td><td style="text-align: center; width: 180px; ">生产环境</td><td style="text-align: center; width: 180px; ">办公终端</td><td style="text-align: center; width: 150px; ">完成</td><td style="text-align: center; width: 100px; "><a style="text-decoration: underline; white-space: normal; word-wrap: break-word; display: inline-block; max-width: 200px;" onclick="ckLc(252379)">查看</a></td><td style="text-align: center; width: 180px; "></td></tr></tbody></table><div class="fixed-table-border" style="width: 1661.33px; height: 448.692px;"></div></div>
      <div class="fixed-table-footer" style="display: none;"></div>
      </div>
      <div class="fixed-table-pagination" style=""><div class="pull-left pagination-detail"><span class="pagination-info">
      显示第 1 到第 15 条记录，总共 151 条记录
      </span><div class="page-list">每页显示 <div class="btn-group dropdown dropup">
        <button class="btn btn-default btn-outline dropdown-toggle" type="button" data-toggle="dropdown">
        <span class="page-size">
        15
        </span>
        <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu"><li role="menuitem" class="active"><a href="http://*************:8075/YCXXYWXT/jrsqAction/fhqcl#">15</a></li><li role="menuitem" class=""><a href="http://*************:8075/YCXXYWXT/jrsqAction/fhqcl#">25</a></li><li role="menuitem" class=""><a href="http://*************:8075/YCXXYWXT/jrsqAction/fhqcl#">50</a></li></ul></div> 条记录</div></div><div class="pull-right pagination"><ul class="pagination pagination-outline"><li class="page-item page-pre disabled"><a class="page-link" aria-label="上一页" href="javascript:void(0)">‹</a></li><li class="page-item active"><a class="page-link" aria-label="第1页" href="javascript:void(0)">1</a></li><li class="page-item"><a class="page-link" aria-label="第2页" href="javascript:void(0)">2</a></li><li class="page-item"><a class="page-link" aria-label="第3页" href="javascript:void(0)">3</a></li><li class="page-item"><a class="page-link" aria-label="第4页" href="javascript:void(0)">4</a></li><li class="page-item"><a class="page-link" aria-label="第5页" href="javascript:void(0)">5</a></li><li class="page-item page-last-separator disabled"><a class="page-link" aria-label="" href="javascript:void(0)">...</a></li><li class="page-item"><a class="page-link" aria-label="第11页" href="javascript:void(0)">11</a></li><li class="page-item page-next"><a class="page-link" aria-label="下一页" href="javascript:void(0)">›</a></li></ul></div></div>
      </div><div class="clearfix"></div>
        </div>
    </div>
</div>

    <script> var ctx = "\/YCXXYWXT\/"; var lockscreen = null; if(lockscreen){window.top.location=ctx+"lockscreen";} </script>
    <a id="scroll-up" href="javascript:;" class="btn btn-sm display" style="cursor: pointer; display: none; position: fixed; right: 15px; bottom: 5px;"><i class="fa fa-angle-double-up"></i></a>
	<script src="./防火墙策略_files/jquery.min.js.下载"></script>
	<script src="./防火墙策略_files/bootstrap.min.js.下载"></script>
	<!-- bootstrap-table 表格插件 -->
	<script src="./防火墙策略_files/bootstrap-table.min.js.下载"></script>
	<script src="./防火墙策略_files/bootstrap-table-zh-CN.min.js.下载"></script>
	<script src="./防火墙策略_files/bootstrap-table-mobile.js.下载"></script>
	<!-- jquery-validate 表单验证插件 -->
	<script src="./防火墙策略_files/jquery.validate.min.js.下载"></script>
	<script src="./防火墙策略_files/jquery.validate.extend.js.下载"></script>
	<script src="./防火墙策略_files/messages_zh.js.下载"></script>
	<!-- bootstrap-table 表格树插件 -->
	<script src="./防火墙策略_files/bootstrap-table-tree.min.js.下载"></script>
	<!-- 遮罩层 -->
	<script src="./防火墙策略_files/jquery.blockUI.js.下载"></script>
    <script src="./防火墙策略_files/icheck.min.js.下载"></script>
	<script src="./防火墙策略_files/layer.min.js.下载"></script>
	<script src="./防火墙策略_files/layui.min.js.下载"></script>
	<script src="./防火墙策略_files/common.js.下载"></script>
	<script src="./防火墙策略_files/ry-ui.js.下载"></script>


    <script src="./防火墙策略_files/bootstrap-datetimepicker.min.js.下载"></script>

<script>
    var prefix = ctx + "jrsqAction";
    var filePrefix = ctx + "lcqdAction";
    var LcPrefix = ctx + "dbsyAction";

    $(function() {

        var options = {
            url: prefix + "/getRecordsFhqcl",
            height: $(window).height() * 0.85,
            modalName: '防火墙策略',
            pageSize: 15,
            pageNumber: 1,
            pageList: [15, 25, 50],
            columns: [{
                checkbox: true
            },
                {
                    field: 'ID',
                    title: 'ID',
                    visible: false,
                },
                {
                    field: 'SQR',
                    title: '申请人',
                    width: '150',
                    align: 'center',
                },
                {
                    field: 'SQSJ',
                    title: '申请时间',
                    width: '180',
                    align: 'center',
                },
                {
                    field: 'MDHJ',
                    title: '目的业务运行环境',
                    width: '180',
                    align: 'center',
                },
                {
                    field: 'YLX',
                    title: '源类型',
                    width: '180',
                    align: 'center',
                },
                {
                    field: 'ZT',
                    title: '状态',
                    visible: false
                },
                {
                    field: 'ZT_MC',
                    title: '流程状态',
                    width: '150',
                    align: 'center',
                },
                {
                    field: 'lcrz',
                    title: '流程日志',
                    width: '100',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return "<a style='text-decoration: underline; white-space: normal; word-wrap: break-word; " +
                            "display: inline-block; max-width: 200px;' onclick='ckLc("+row.ID+")'>查看</a>";
                    }
                },
                {
                    field: 'CLR',
                    title: '下一步处理人',
                    width: '180',
                    align: 'center',
                }]
        };
        $.table.init(options);
    });



    /*新增*/
    function add(width, height) {
        openSp("新增申请", prefix + "/addFhqcl", width, height);
    }

    function openSp(title,url,width,height) {
        var options = {
            url: url,
            title: title,
            width: width,
            height: height,
            btn: 0
        }
        $.modal.openOptions(options);
    }


    // 查看详情
    function ckxq() {
        var allrows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (allrows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var id = allrows[0].ID;
        open("查看信息", prefix + "/ckFhqcl?id=" + id, 1200, 800, function (index) {
            top.layer.close(index)   // 关闭弹框
            $.table.search();
        });
    }

    /*流程查看*/

    function ckLc(ID) {
        open("审核流程信息", LcPrefix +"/viewLc?ID="+ID, 900, 500, function (index) {
            top.layer.close(index)   // 关闭弹框
            $.table.search();
        });
    }



    // 修改查看页面按钮
    function open(title, url, width, height, callback) {
        // 如果是移动端，就使用自适应大小弹窗
        if ($.common.isMobile()) {
            width = 'auto';
            height = 'auto';
        }
        if ($.common.isEmpty(title)) {
            title = false;
        }
        if ($.common.isEmpty(url)) {
            url = "/404.html";
        }
        if ($.common.isEmpty(width)) {
            width = 800;
        }
        if ($.common.isEmpty(height)) {
            height = ($(window).height() - 50);
        }
        if ($.common.isEmpty(callback)) {
            callback = function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            }
        }
        top.layer.open({
            type: 2,
            area: [width + 'px', height + 'px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: title,
            content: url,
            btn: ['关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: callback,
            cancel: function (index) {
                return true;
            },
            success: function () {
                $(':focus').blur();
            }
        });
    }
    /**
     * 删除行
     */
    var prefix_xt = ctx + "xtzhsqAction";
    function deleteRow(){
        var selects = $("#bootstrap-table").bootstrapTable('getSelections');
        if(selects.length <= 0){
            $.modal.alertWarning("请选择一个或多个数据进行删除！");
            return;
        }
        var allowedStatus = ["1", "4", "5", "76", "22", "24", "26"];
        for (var i = 0; i < selects.length; i++) {
            if (!allowedStatus.includes(selects[i].ZT)) {
                $.modal.alertWarning("该状态无法删除");
                return;
            }
        }
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        $.modal.confirm("是否确认删除已选择记录?", function() {
            var ids = rows.join();
            console.log("====>",ids)
            var url = prefix_xt + "/delRecord";
            var data = {"ids": ids,"ywlx":"sqd"};
            $.operate.submit(url, "post", "json", data);
        });
    }
</script>


<div class="layui-layer-move" id="layui-layer-move" style="cursor: move; display: none;"></div><div class="layui-layer-shade" id="layui-layer-shade9" times="9" style="z-index: 19891022; background-color: rgb(0, 0, 0); opacity: 0.3;"></div><div class="layui-layer layui-layer-iframe layer-ext-moon" id="layui-layer9" type="iframe" times="9" showtime="0" contype="string" style="z-index: 19891023; width: 1200px; height: 800px; position: fixed; top: 236.5px; left: 258.5px;"><div class="layui-layer-title" style="cursor: move;">查看信息</div><div class="layui-layer-content"><iframe scrolling="auto" allowtransparency="true" id="layui-layer-iframe9" name="layui-layer-iframe9" onload="this.className=&#39;&#39;;" class="" frameborder="0" src="./防火墙策略_files/ckFhqcl.html" style="height: 703px;"></iframe></div><div class="layui-layer-setwin"><span class="layui-layer-min"></span><span class="layui-layer-max"></span><span class="layui-icon layui-icon-close layui-layer-close layui-layer-close1"></span></div><div class="layui-layer-btn"><a class="layui-layer-btn0">关闭</a></div><span class="layui-layer-resize"></span></div></body></html>