{"name": "stdin-discarder", "version": "0.1.0", "description": "Discard stdin input except for Ctrl+C", "license": "MIT", "repository": "sindresorhus/stdin-discarder", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["stdin", "process", "standard", "discard", "ignore", "input"], "dependencies": {"bl": "^5.0.0"}, "devDependencies": {"ava": "^4.3.0", "hook-std": "^3.0.0", "tsd": "^0.21.0", "xo": "^0.50.0"}}