<script lang="ts" setup>
import { ref } from 'vue';

// 定义事件
const emit = defineEmits(['navigate']);

// 导航到指定页面
function navigateTo(page: string) {
  emit('navigate', page);
}
</script>

<template>
  <div class="home-page">
    <p class="description">请选择一种信息获取方式：</p>
    
    <div class="method-buttons">
      <button 
        @click="navigateTo('screenshot')" 
        class="method-btn screenshot-btn"
      >
        <div class="btn-icon">📷</div>
        <div class="btn-content">
          <div class="btn-title">页面截图方式</div>
          <div class="btn-desc">通过截取当前页面并发送到后端进行解析</div>
        </div>
      </button>
      
      <button 
        @click="navigateTo('dom')" 
        class="method-btn dom-btn"
      >
        <div class="btn-icon">🔍</div>
        <div class="btn-content">
          <div class="btn-title">DOM解析方式</div>
          <div class="btn-desc">通过直接解析页面DOM结构获取信息</div>
        </div>
      </button>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  height: 100%;
}

.description {
  color: #666;
  margin: 0 0 1rem;
  text-align: center;
  font-size: 0.9rem;
}

.method-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.method-btn {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.method-btn:hover:not(:disabled) {
  border-color: #2196F3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.method-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #f9f9f9;
}

.btn-icon {
  font-size: 1.8rem;
  margin-right: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
}

.btn-content {
  flex: 1;
}

.btn-title {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.2rem;
  color: #333;
}

.btn-desc {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.3;
}

.screenshot-btn .btn-icon {
  color: #4CAF50;
}

.dom-btn .btn-icon {
  color: #FF9800;
}
</style> 