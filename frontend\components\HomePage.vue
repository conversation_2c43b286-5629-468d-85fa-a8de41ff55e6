<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { getApiUrl, API_CONFIG, updateApiConfig, testApiConnection } from './config';

// 定义事件
const emit = defineEmits(['navigate']);

// API配置相关状态
const showApiConfig = ref(false);
const apiHost = ref('127.0.0.1');
const apiPort = ref('8000');
const testLoading = ref(false);
const testResult = ref<{ success: boolean; message: string } | null>(null);

// 导航到指定页面
function navigateTo(page: string) {
  emit('navigate', page);
}

// 显示/隐藏API配置
function toggleApiConfig() {
  showApiConfig.value = !showApiConfig.value;
  if (showApiConfig.value) {
    loadCurrentConfig();
  }
}

// 加载当前配置
function loadCurrentConfig() {
  const currentUrl = new URL(API_CONFIG.BASE_URL);
  apiHost.value = currentUrl.hostname;
  apiPort.value = currentUrl.port || '8000';
}

// 保存API配置
function saveApiConfig() {
  const newBaseUrl = `http://${apiHost.value}:${apiPort.value}`;
  updateApiConfig(newBaseUrl);
  testResult.value = null;
  alert('API配置已保存');
}

// 测试API连接
async function testConnection() {
  try {
    testLoading.value = true;
    testResult.value = null;

    const tempBaseUrl = `http://${apiHost.value}:${apiPort.value}`;
    const result = await testApiConnection(tempBaseUrl);

    testResult.value = {
      success: result.success,
      message: result.success ? '连接成功！' : `连接失败: ${result.error}`
    };
  } catch (error) {
    testResult.value = {
      success: false,
      message: `测试失败: ${error instanceof Error ? error.message : String(error)}`
    };
  } finally {
    testLoading.value = false;
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadCurrentConfig();
});
</script>

<template>
  <div class="home-page">
    <p class="description">请选择页面解析方式：</p>

    <div class="method-buttons">
      <button
        @click="navigateTo('strategy')"
        class="method-btn strategy-btn"
      >
        <div class="btn-icon">🛡️</div>
        <div class="btn-content">
          <div class="btn-title">Strategy页面解析</div>
          <div class="btn-desc">解析Strategy防火墙页面信息</div>
        </div>
      </button>

      <button
        @click="navigateTo('hillstone')"
        class="method-btn hillstone-btn"
      >
        <div class="btn-icon">🔥</div>
        <div class="btn-content">
          <div class="btn-title">Hillstone页面解析</div>
          <div class="btn-desc">解析Hillstone防火墙页面信息</div>
        </div>
      </button>
    </div>

    <!-- API配置区域 -->
    <div class="api-config-section">
      <button @click="toggleApiConfig" class="config-toggle-btn">
        {{ showApiConfig ? '隐藏' : '显示' }} API配置
      </button>

      <div v-if="showApiConfig" class="api-config-panel">
        <h3>API服务器配置</h3>
        <div class="config-form">
          <div class="form-group">
            <label>服务器地址:</label>
            <input
              v-model="apiHost"
              type="text"
              placeholder="127.0.0.1"
              class="config-input"
            />
          </div>
          <div class="form-group">
            <label>端口:</label>
            <input
              v-model="apiPort"
              type="text"
              placeholder="8000"
              class="config-input"
            />
          </div>
          <div class="config-actions">
            <button @click="saveApiConfig" class="save-btn">保存配置</button>
            <button
              @click="testConnection"
              :disabled="testLoading"
              class="test-btn"
            >
              {{ testLoading ? '测试中...' : '测试连接' }}
            </button>
          </div>
          <div v-if="testResult" class="test-result" :class="{ success: testResult.success, error: !testResult.success }">
            {{ testResult.message }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  height: 100%;
}

.description {
  color: #666;
  margin: 0 0 1rem;
  text-align: center;
  font-size: 0.9rem;
}

.method-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.method-btn {
  display: flex;
  align-items: center;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.method-btn:hover:not(:disabled) {
  border-color: #2196F3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.method-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #f9f9f9;
}

.btn-icon {
  font-size: 1.8rem;
  margin-right: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
}

.btn-content {
  flex: 1;
}

.btn-title {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.2rem;
  color: #333;
}

.btn-desc {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.3;
}

.strategy-btn .btn-icon {
  color: #4CAF50;
}

.hillstone-btn .btn-icon {
  color: #FF9800;
}

/* API配置样式 */
.api-config-section {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.config-toggle-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.config-toggle-btn:hover {
  background-color: #5a6268;
}

.api-config-panel {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.api-config-panel h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.form-group label {
  font-weight: bold;
  color: #555;
  font-size: 0.9rem;
}

.config-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.config-input:focus {
  border-color: #2196F3;
  outline: none;
}

.config-actions {
  display: flex;
  gap: 0.5rem;
}

.save-btn, .test-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

.save-btn:hover {
  background-color: #218838;
}

.test-btn {
  background-color: #007bff;
  color: white;
}

.test-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.test-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.test-result {
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.test-result.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.test-result.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
</style> 