// 存储键名
const STORAGE_KEY = 'api_config_base_url';

// API配置
export const API_CONFIG = {
  // 后端API基础URL，默认值
  BASE_URL: 'http://127.0.0.1:8000',

  // API端点
  ENDPOINTS: {
    // 健康检查API
    HEALTH_CHECK: '/health',
    // Strategy页面解析API
    STRATEGY_PARSE: '/strategy/parse',
    // Hillstone页面解析API
    HILLSTONE_PARSE: '/hillstone/parse',
    // 表单验证API
    FORM_VALIDATE: '/form/validate'
  }
};

// 初始化配置 - 从localStorage加载
function initConfig() {
  try {
    const savedUrl = localStorage.getItem(STORAGE_KEY);
    if (savedUrl) {
      API_CONFIG.BASE_URL = savedUrl;
    }
  } catch (error) {
    console.error('加载API配置失败:', error);
  }
}

// 更新API配置
export function updateApiConfig(baseUrl: string): void {
  try {
    API_CONFIG.BASE_URL = baseUrl;
    localStorage.setItem(STORAGE_KEY, baseUrl);
    console.log('API配置已更新:', baseUrl);
  } catch (error) {
    console.error('保存API配置失败:', error);
    throw new Error('保存配置失败');
  }
}

// 获取完整的API URL
export function getApiUrl(endpoint: string): string {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
}

// 测试API连接
export async function testApiConnection(baseUrl?: string): Promise<{ success: boolean; error?: string }> {
  try {
    const testUrl = baseUrl || API_CONFIG.BASE_URL;
    const healthCheckUrl = `${testUrl}${API_CONFIG.ENDPOINTS.HEALTH_CHECK}`;

    console.log('测试API连接:', healthCheckUrl);

    const response = await fetch(healthCheckUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      // 设置超时时间
      signal: AbortSignal.timeout(5000)
    });

    if (response.ok) {
      return { success: true };
    } else {
      return {
        success: false,
        error: `服务器响应错误: ${response.status} ${response.statusText}`
      };
    }
  } catch (error) {
    console.error('API连接测试失败:', error);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return { success: false, error: '连接超时' };
      } else if (error.message.includes('fetch')) {
        return { success: false, error: '无法连接到服务器' };
      } else {
        return { success: false, error: error.message };
      }
    }

    return { success: false, error: '未知错误' };
  }
}

// 初始化配置
initConfig();