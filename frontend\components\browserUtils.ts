// 浏览器检测工具
export interface BrowserInfo {
  isFirefox: boolean;
  isChrome: boolean;
  isEdge: boolean;
  browserName: string;
  supportsScreenshot: boolean;
  supportsSidePanel: boolean;
}

/**
 * 检测当前浏览器类型和功能支持
 */
export function detectBrowser(): BrowserInfo {
  // 检测Firefox
  const isFirefox = typeof browser !== 'undefined' && 
    (browser.runtime.getBrowserInfo !== undefined || 
     navigator.userAgent.includes('Firefox'));

  // 检测Edge
  const isEdge = navigator.userAgent.includes('Edg/');
  
  // 检测Chrome (包括基于Chromium的浏览器，但排除Edge)
  const isChrome = !isFirefox && !isEdge && 
    (navigator.userAgent.includes('Chrome') || 
     typeof chrome !== 'undefined');

  // 确定浏览器名称
  let browserName = 'Unknown';
  if (isFirefox) browserName = 'Firefox';
  else if (isEdge) browserName = 'Edge';
  else if (isChrome) browserName = 'Chrome';

  // 检测功能支持
  const supportsScreenshot = typeof browser !== 'undefined' && 
    browser.tabs && 
    typeof browser.tabs.captureVisibleTab === 'function';

  const supportsSidePanel = isChrome && 
    typeof browser !== 'undefined' && 
    browser.sidePanel !== undefined;

  return {
    isFirefox,
    isChrome: isChrome || isEdge, // Edge基于Chromium，API兼容
    isEdge,
    browserName,
    supportsScreenshot,
    supportsSidePanel
  };
}

/**
 * 获取浏览器特定的API调用方式
 */
export function getBrowserAPI() {
  const browserInfo = detectBrowser();
  
  return {
    ...browserInfo,
    // 截图API
    captureTab: async (windowId?: number, options?: any) => {
      if (!browserInfo.supportsScreenshot) {
        throw new Error('当前浏览器不支持截图功能');
      }
      
      if (browserInfo.isFirefox) {
        // Firefox 可能需要不同的参数处理
        return browser.tabs.captureVisibleTab(windowId, options);
      } else {
        // Chrome/Edge
        return browser.tabs.captureVisibleTab(windowId, options);
      }
    },
    
    // 获取当前标签页
    getCurrentTab: async () => {
      return browser.tabs.query({ active: true, currentWindow: true });
    },
    
    // 执行脚本
    executeScript: async (tabId: number, func: Function) => {
      if (browserInfo.isFirefox) {
        // Firefox 可能需要不同的脚本执行方式
        return browser.scripting.executeScript({
          target: { tabId },
          func
        });
      } else {
        // Chrome/Edge
        return browser.scripting.executeScript({
          target: { tabId },
          func
        });
      }
    }
  };
}

/**
 * 显示浏览器特定的错误消息
 */
export function getBrowserSpecificErrorMessage(error: string): string {
  const browserInfo = detectBrowser();
  
  if (browserInfo.isFirefox) {
    return `Firefox: ${error}`;
  } else if (browserInfo.isEdge) {
    return `Edge: ${error}`;
  } else if (browserInfo.isChrome) {
    return `Chrome: ${error}`;
  }
  
  return error;
}

/**
 * 检查是否为特殊页面（不同浏览器可能有不同的特殊页面）
 */
export function isSpecialPage(url: string): boolean {
  const browserInfo = detectBrowser();
  
  const commonSpecialPages = [
    '',
    'about:blank'
  ];
  
  const chromeSpecialPages = [
    'chrome://',
    'chrome-extension://',
    'edge://'
  ];
  
  const firefoxSpecialPages = [
    'about:',
    'moz-extension://'
  ];
  
  // 检查通用特殊页面
  if (commonSpecialPages.some(page => url === page)) {
    return true;
  }
  
  // 检查浏览器特定的特殊页面
  if (browserInfo.isFirefox) {
    return firefoxSpecialPages.some(prefix => url.startsWith(prefix));
  } else {
    return chromeSpecialPages.some(prefix => url.startsWith(prefix));
  }
}
