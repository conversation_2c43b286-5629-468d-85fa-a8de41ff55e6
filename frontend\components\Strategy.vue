<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { getApiUrl, API_CONFIG } from './config';
import { marked } from 'marked';
import { detectBrowser, getBrowserAPI, isSpecialPage } from './browserUtils';

// 定义事件
const emit = defineEmits(['back']);

// 存储键名
const STORAGE_KEYS = {
  FORM_DATA: 'strategy_form_data',
  VALIDATION_RESPONSE: 'strategy_validation_response',
  SUBMIT_SUCCESS: 'strategy_submit_success'
};

// 状态变量
const loading = ref(false);
const error = ref<string | null>(null);
const formData = ref<Array<{key: string, value: string}>>([]);
const currentUrl = ref<string>('');
const apiLoading = ref(false);
const apiError = ref<string | null>(null);
const submitSuccess = ref(false);
const validationResponse = ref<string | null>(null);

// 浏览器信息
const browserInfo = ref(detectBrowser());
const browserAPI = getBrowserAPI();

// 计算属性：将validationResponse转换为HTML
const parsedValidationResponse = computed(() => {
  if (!validationResponse.value) return '';
  return marked(validationResponse.value);
});

// 保存数据到localStorage
function saveToStorage() {
  try {
    localStorage.setItem(STORAGE_KEYS.FORM_DATA, JSON.stringify(formData.value));
    localStorage.setItem(STORAGE_KEYS.VALIDATION_RESPONSE, validationResponse.value || '');
    localStorage.setItem(STORAGE_KEYS.SUBMIT_SUCCESS, JSON.stringify(submitSuccess.value));
  } catch (err) {
    console.error('保存数据到localStorage失败:', err);
  }
}

// 从localStorage加载数据
function loadFromStorage() {
  try {
    const storedFormData = localStorage.getItem(STORAGE_KEYS.FORM_DATA);
    const storedValidationResponse = localStorage.getItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    const storedSubmitSuccess = localStorage.getItem(STORAGE_KEYS.SUBMIT_SUCCESS);

    if (storedFormData) {
      formData.value = JSON.parse(storedFormData);
    }
    if (storedValidationResponse) {
      validationResponse.value = storedValidationResponse;
    }
    if (storedSubmitSuccess) {
      submitSuccess.value = JSON.parse(storedSubmitSuccess);
    }
  } catch (err) {
    console.error('从localStorage加载数据失败:', err);
  }
}

// 清除存储的数据
function clearStorage() {
  try {
    localStorage.removeItem(STORAGE_KEYS.FORM_DATA);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_RESPONSE);
    localStorage.removeItem(STORAGE_KEYS.SUBMIT_SUCCESS);
    
    formData.value = [];
    validationResponse.value = null;
    submitSuccess.value = false;
    apiError.value = null;
  } catch (err) {
    console.error('清除localStorage数据失败:', err);
  }
}

// 返回主页
function goBack() {
  emit('back');
}

// 在组件挂载时获取当前页面信息和存储的数据
onMounted(async () => {
  loadFromStorage();
  
  try {
    const tabs = await browserAPI.getCurrentTab();
    if (tabs && tabs.length > 0 && tabs[0].url) {
      currentUrl.value = tabs[0].url;
      
      const isCurrentPageSpecial = isSpecialPage(currentUrl.value);
      
      console.log(`当前浏览器: ${browserInfo.value.browserName}`);
      console.log(`当前页面: ${currentUrl.value}`);
      console.log(`是否为特殊页面: ${isCurrentPageSpecial}`);
      
      if (isCurrentPageSpecial) {
        error.value = `当前页面是${browserInfo.value.browserName}浏览器特殊页面，无法解析DOM结构。`;
        return;
      }
    }
  } catch (err) {
    console.error('获取当前页面信息失败', err);
    error.value = `获取当前页面信息失败: ${err instanceof Error ? err.message : String(err)}`;
  }
});

// 解析Strategy页面DOM结构
async function parseStrategyPage() {
  try {
    loading.value = true;
    error.value = null;
    
    const tabs = await browserAPI.getCurrentTab();
    if (!tabs || tabs.length === 0) {
      throw new Error('无法获取当前标签页');
    }
    
    console.log(`开始解析Strategy页面 (${browserInfo.value.browserName})`);
    
    // 在当前标签页执行脚本，解析Strategy页面结构
    const result = await browserAPI.executeScript(tabs[0].id as number, extractStrategyData);
    
    console.log('Strategy解析结果原始数据:', result);
    
    if (result && result[0] && result[0].result) {
      formData.value = result[0].result.map((item, index) => {
        if (typeof item !== 'object' || !item.key) {
          return {
            key: `字段_${index + 1}`,
            value: typeof item === 'object' && item.value ? item.value : String(item)
          };
        }
        return item;
      });
      
      saveToStorage();
      console.log('处理后的Strategy数据:', formData.value);
    }
    
    if (formData.value.length === 0) {
      error.value = '未在Strategy页面中找到可解析的数据';
    }
  } catch (err) {
    console.error('解析Strategy页面失败:', err);
    error.value = err instanceof Error ? err.message : '解析Strategy页面时发生未知错误';
  } finally {
    loading.value = false;
  }
}

// 发送数据到后端
async function submitToBackend() {
  if (formData.value.length === 0) {
    apiError.value = '没有数据可提交';
    return;
  }
  
  try {
    apiLoading.value = true;
    apiError.value = null;
    submitSuccess.value = false;
    validationResponse.value = null;
    
    const invalidEntries = formData.value.filter(item => !item.key.trim() || !item.value.trim());
    if (invalidEntries.length > 0) {
      throw new Error('存在空的键或值，请检查数据');
    }
    
    const dataToSend = {
      items: formData.value.map(item => ({
        key: item.key.trim(),
        value: item.value.trim()
      }))
    };
    
    console.log('发送Strategy数据到后端:', dataToSend);
    
    const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.STRATEGY_PARSE), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(dataToSend)
    });
    
    const result = await response.json();
    console.log('后端响应:', result);
    
    if (result.code === 400) {
      submitSuccess.value = true;
      validationResponse.value = result.data || '未收到具体的修改建议。';
      saveToStorage();
    } else {
      throw new Error(result.msg || '提交失败');
    }
    
  } catch (err) {
    console.error('发送Strategy数据到后端失败:', err);
    apiError.value = err instanceof Error ? err.message : '发送数据时发生未知错误';
    submitSuccess.value = false;
    validationResponse.value = null;
    saveToStorage();
  } finally {
    apiLoading.value = false;
  }
}

// 在页面中执行的脚本，用于提取Strategy页面数据
function extractStrategyData() {
  try {
    const strategyData = [];

    // 首先尝试使用特定的iframe选择器
    console.log('开始查找特定的iframe...');
    const targetIframe = document.querySelector(".layui-layer.layui-layer-iframe.layer-ext-moon");
    console.log('找到的iframe:', targetIframe);

    if (targetIframe) {
      strategyData.push({
        key: '目标iframe状态',
        value: '找到了layui-layer iframe'
      });

      // 打印iframe的详细信息
      strategyData.push({
        key: 'iframe详细信息',
        value: `ID: ${targetIframe.id || '无'}, Class: ${targetIframe.className || '无'}, TagName: ${targetIframe.tagName}`
      });

      // 查找iframe内的实际iframe元素
      const innerIframe = targetIframe.querySelector('iframe');
      console.log('iframe内的iframe元素:', innerIframe);

      if (innerIframe) {
        strategyData.push({
          key: '内部iframe信息',
          value: `ID: ${innerIframe.id || '无'}, Src: ${innerIframe.src || '无'}, Name: ${innerIframe.name || '无'}`
        });

        // 尝试访问内部iframe的内容
        try {
          let iframeDoc = null;
          if (innerIframe.contentDocument) {
            iframeDoc = innerIframe.contentDocument;
            strategyData.push({
              key: 'iframe访问方式',
              value: 'contentDocument访问成功'
            });
          } else if (innerIframe.contentWindow && innerIframe.contentWindow.document) {
            iframeDoc = innerIframe.contentWindow.document;
            strategyData.push({
              key: 'iframe访问方式',
              value: 'contentWindow.document访问成功'
            });
          }

          if (iframeDoc) {
            // 获取iframe的完整HTML内容
            const iframeHTML = iframeDoc.documentElement.outerHTML;

            strategyData.push({
              key: 'iframe页面标题',
              value: iframeDoc.title || '无标题'
            });

            strategyData.push({
              key: 'iframe页面URL',
              value: iframeDoc.URL || '无URL'
            });

            // 分段显示iframe的HTML内容
            const chunkSize = 8000;
            const totalChunks = Math.ceil(iframeHTML.length / chunkSize);

            for (let i = 0; i < totalChunks; i++) {
              const start = i * chunkSize;
              const end = Math.min(start + chunkSize, iframeHTML.length);
              const chunk = iframeHTML.substring(start, end);

              strategyData.push({
                key: `iframe HTML内容 第${i + 1}段（共${totalChunks}段）`,
                value: chunk
              });
            }

            // 在iframe中查找目标字段
            const fieldMappings = [
              { name: 'ID', label: '申请ID' },
              { name: 'SQBMMC', label: '申请部门' },
              { name: 'SQSJ', label: '申请日期' },
              { name: 'SQR', label: '申请人' },
              { name: 'LXDH', label: '申请人联系电话' },
              { name: 'MDHJ', label: '目的运行环境' },
              { name: 'YLX', label: '源类型' },
              { name: 'kSSJ', label: '有效期开始' },
              { name: 'JSSJ', label: '有效期结束' },
              { name: 'IP', label: '源地址' },
              { name: 'YWL', label: '源端口' },
              { name: 'MDDZ', label: '目的地址' },
              { name: 'MDDK', label: '目的端口' },
              { name: 'DKLX', label: '端口类型' },
              { name: 'YDZMS', label: '源地址描述' },
              { name: 'MDDZMS', label: '目的地址描述' },
              { name: 'BZ', label: '备注' }
            ];

            strategyData.push({
              key: '=== 开始提取iframe中的字段 ===',
              value: '以下是从iframe中提取的表单字段'
            });

            fieldMappings.forEach(field => {
              const element = iframeDoc.querySelector(`input[name="${field.name}"], textarea[name="${field.name}"]`);

              if (element) {
                let value = '';
                if (element.tagName.toLowerCase() === 'input') {
                  value = element.value || '';
                } else if (element.tagName.toLowerCase() === 'textarea') {
                  value = element.value || element.textContent || '';
                }

                strategyData.push({
                  key: field.label + ' (来自iframe)',
                  value: value.trim()
                });

                console.log(`找到字段 ${field.name}: ${value.trim()}`);
              } else {
                console.log(`未找到字段: ${field.name}`);
              }
            });

          } else {
            strategyData.push({
              key: 'iframe访问失败',
              value: '无法访问iframe文档内容，可能存在跨域限制'
            });
          }

        } catch (error) {
          strategyData.push({
            key: 'iframe访问错误',
            value: `访问iframe内容时出错: ${error.message}`
          });
          console.error('访问iframe内容时出错:', error);
        }

      } else {
        strategyData.push({
          key: 'iframe查找结果',
          value: '在layui-layer容器中未找到iframe元素'
        });
      }

    } else {
      // 如果没找到特定的iframe，尝试查找所有iframe
      strategyData.push({
        key: '目标iframe状态',
        value: '未找到layui-layer iframe，查找所有iframe'
      });

      const allIframes = document.querySelectorAll('iframe');
      strategyData.push({
        key: '所有iframe数量',
        value: allIframes.length.toString()
      });

      allIframes.forEach((iframe, index) => {
        strategyData.push({
          key: `iframe${index + 1}信息`,
          value: `ID: ${iframe.id || '无'}, Class: ${iframe.className || '无'}, Src: ${iframe.src || '无'}`
        });
      });
    }

    // 添加页面基本信息
    strategyData.push({
      key: '页面标题',
      value: document.title || '无标题'
    });

    strategyData.push({
      key: '页面URL',
      value: window.location.href
    });

    strategyData.push({
      key: '提取时间',
      value: new Date().toLocaleString('zh-CN')
    });

    // 如果没有通过特定iframe找到数据，尝试在主页面查找
    if (!targetIframe) {
      strategyData.push({
        key: '=== 在主页面查找字段 ===',
        value: '尝试在主页面中查找表单字段'
      });

      const fieldMappings = [
        { name: 'ID', label: '申请ID' },
        { name: 'SQBMMC', label: '申请部门' },
        { name: 'SQSJ', label: '申请日期' },
        { name: 'SQR', label: '申请人' },
        { name: 'LXDH', label: '申请人联系电话' },
        { name: 'MDHJ', label: '目的运行环境' },
        { name: 'YLX', label: '源类型' },
        { name: 'kSSJ', label: '有效期开始' },
        { name: 'JSSJ', label: '有效期结束' },
        { name: 'IP', label: '源地址' },
        { name: 'YWL', label: '源端口' },
        { name: 'MDDZ', label: '目的地址' },
        { name: 'MDDK', label: '目的端口' },
        { name: 'DKLX', label: '端口类型' },
        { name: 'YDZMS', label: '源地址描述' },
        { name: 'MDDZMS', label: '目的地址描述' },
        { name: 'BZ', label: '备注' }
      ];

      fieldMappings.forEach(field => {
        const element = document.querySelector(`input[name="${field.name}"], textarea[name="${field.name}"]`);

        if (element) {
          let value = '';
          if (element.tagName.toLowerCase() === 'input') {
            value = element.value || '';
          } else if (element.tagName.toLowerCase() === 'textarea') {
            value = element.value || element.textContent || '';
          }

          strategyData.push({
            key: field.label + ' (主页面)',
            value: value.trim()
          });
        }
      });
    }

    // 尝试使用postMessage与跨域iframe通信
    function tryPostMessageCommunication() {
      const iframes = document.querySelectorAll('iframe');
      let postMessageAttempts = [];

      iframes.forEach((iframe, index) => {
        try {
          // 检查是否是跨域iframe
          let isCrossOrigin = false;
          try {
            iframe.contentDocument;
          } catch (e) {
            if (e.name === 'SecurityError') {
              isCrossOrigin = true;
            }
          }

          if (isCrossOrigin && iframe.contentWindow) {
            // 尝试向iframe发送消息请求数据
            const message = {
              type: 'EXTRACT_FORM_DATA',
              fields: fieldMappings.map(f => f.name),
              timestamp: Date.now()
            };

            iframe.contentWindow.postMessage(message, '*');

            postMessageAttempts.push({
              iframeIndex: index + 1,
              iframeId: iframe.id || '无',
              message: '已发送postMessage请求',
              sent: true
            });
          }
        } catch (error) {
          postMessageAttempts.push({
            iframeIndex: index + 1,
            iframeId: iframe.id || '无',
            message: `postMessage失败: ${error.message}`,
            sent: false
          });
        }
      });

      return postMessageAttempts;
    }

    // 执行postMessage尝试
    const postMessageResults = tryPostMessageCommunication();

    if (postMessageResults.length > 0) {
      strategyData.push({
        key: 'PostMessage通信尝试',
        value: `向 ${postMessageResults.length} 个iframe发送了数据提取请求`
      });

      postMessageResults.forEach(result => {
        strategyData.push({
          key: `iframe${result.iframeIndex} PostMessage`,
          value: result.message
        });
      });

      strategyData.push({
        key: 'PostMessage说明',
        value: '如果iframe支持postMessage通信，数据将在几秒后显示。请刷新页面查看结果。'
      });
    }

    // 如果没有提取到任何有效数据，提供详细的调试信息
    const validDataCount = strategyData.filter(item =>
      !item.key.includes('调试') &&
      !item.key.includes('PostMessage') &&
      !item.key.includes('未找到') &&
      item.value.trim() !== ''
    ).length;

    if (validDataCount === 0) {
      console.log('未找到任何预定义字段，开始全面扫描...');

      // 扫描所有input和textarea元素
      const allElements = document.querySelectorAll('input, textarea');

      strategyData.push({
        key: '调试信息',
        value: `页面中找到 ${allElements.length} 个输入元素`
      });

      // 列出所有input和textarea的详细信息
      allElements.forEach((element, index) => {
        const info = {
          index: index + 1,
          tagName: element.tagName,
          type: element.type || '无',
          id: element.id || '无',
          name: element.name || '无',
          value: element.value || '无',
          className: element.className || '无'
        };

        strategyData.push({
          key: `元素${index + 1}`,
          value: `${info.tagName}[${info.type}] ID:${info.id} Name:${info.name} Value:${info.value} Class:${info.className}`
        });

        console.log(`元素${index + 1}:`, info);
      });
    }

    // 如果找到了一些数据但都是数字，可能是索引问题
    const hasOnlyNumbers = strategyData.length > 0 &&
      strategyData.every(item => /^\d+$/.test(item.value.trim()));

    if (hasOnlyNumbers) {
      console.log('检测到只有数字值，可能存在提取错误');

      // 添加警告信息
      strategyData.unshift({
        key: '⚠️ 警告',
        value: '检测到提取的值都是数字，可能存在提取错误。请查看下方的调试信息。'
      });

      // 重新尝试更精确的提取
      const preciseElements = document.querySelectorAll('input[name], textarea[name]');
      preciseElements.forEach((element, index) => {
        if (element.name && element.value) {
          strategyData.push({
            key: `精确提取${index + 1}`,
            value: `Name:${element.name} = Value:${element.value}`
          });
        }
      });
    }

    // 添加页面基本信息
    strategyData.unshift({
      key: '页面标题',
      value: document.title || '防火墙申请表单'
    });

    strategyData.push({
      key: '页面URL',
      value: window.location.href
    });

    strategyData.push({
      key: '提取时间',
      value: new Date().toLocaleString('zh-CN')
    });

    return strategyData;

  } catch (error) {
    console.error('提取Strategy数据时出错:', error);
    return [{ key: '解析错误', value: error.message || '未知错误' }];
  }

  // 辅助函数：获取字段标签
  function getFieldLabel(element) {
    try {
      // 方法1: 通过label的for属性
      if (element.id) {
        const label = document.querySelector(`label[for="${element.id}"]`);
        if (label && label.textContent) {
          return label.textContent.trim().replace(/[:：*]$/, '');
        }
      }

      // 方法2: 查找父元素中的label
      const parentLabel = element.closest('label');
      if (parentLabel && parentLabel.textContent) {
        return parentLabel.textContent.trim().replace(/[:：*]$/, '');
      }

      // 方法3: 查找同级或前面的label元素
      const formGroup = element.closest('.form-group');
      if (formGroup) {
        const label = formGroup.querySelector('.control-label');
        if (label && label.textContent) {
          return label.textContent.trim().replace(/[:：*]$/, '');
        }
      }

      // 方法4: 使用name属性
      if (element.name) {
        return element.name;
      }

      // 方法5: 使用id属性
      if (element.id) {
        return element.id;
      }

      return '未知字段';
    } catch (err) {
      return '未知字段';
    }
  }
}

// 编辑键值对
function editItem(index: number, field: 'key' | 'value', value: string) {
  if (index >= 0 && index < formData.value.length) {
    formData.value[index][field] = value;
    saveToStorage();
  }
}

// 删除键值对
function removeItem(index: number) {
  if (index >= 0 && index < formData.value.length) {
    formData.value.splice(index, 1);
    saveToStorage();
  }
}

// 添加新的键值对
function addNewItem() {
  formData.value.push({ key: '', value: '' });
  saveToStorage();
}

// 上移项目
function moveItemUp(index: number) {
  if (index > 0) {
    const item = formData.value[index];
    formData.value.splice(index, 1);
    formData.value.splice(index - 1, 0, item);
    saveToStorage();
  }
}

// 下移项目
function moveItemDown(index: number) {
  if (index < formData.value.length - 1) {
    const item = formData.value[index];
    formData.value.splice(index, 1);
    formData.value.splice(index + 1, 0, item);
    saveToStorage();
  }
}
</script>

<template>
  <div class="strategy-page">
    <div class="header">
      <button @click="goBack" class="back-btn">
        &larr; 返回
      </button>
      <h2>Strategy页面解析</h2>
      <div class="browser-info">
        <span class="browser-badge" :class="browserInfo.browserName.toLowerCase()">
          {{ browserInfo.browserName }}
        </span>
      </div>
    </div>

    <div class="info-box">
      <p>【解析Strategy页面】→ 提取防火墙配置信息；检查数据完整性 → 遗漏时点击【+添加字段】；确认无误 → 【提交数据】生成修改意见</p>
    </div>

    <div v-if="error" class="error">
      <strong>错误：</strong> {{ error }}
    </div>

    <div class="controls">
      <button
        @click="parseStrategyPage"
        :disabled="loading"
        class="parse-btn"
      >
        <span v-if="loading" class="loading-spinner"></span>
        {{ loading ? '解析中...' : '解析Strategy页面' }}
      </button>

      <button
        v-if="formData.length > 0"
        @click="clearStorage"
        class="clear-btn"
        title="清除所有已保存的数据"
      >
        清除数据
      </button>
    </div>

    <div v-if="formData.length > 0" class="form-data-container">
      <div class="form-data-header">
        <h3>解析结果：共 {{ formData.length }} 项</h3>
        <button @click="addNewItem" class="add-btn">
          + 添加字段
        </button>
      </div>

      <div class="form-data-list">
        <div v-if="formData.length === 0" class="empty-state">
          未找到任何数据。您可以点击"添加字段"手动添加。
        </div>

        <div v-for="(item, index) in formData" :key="index" class="form-data-item">
          <div class="item-order-buttons">
            <button
              @click="moveItemUp(index)"
              :disabled="index === 0"
              class="order-btn up-btn"
              title="上移"
            >
              ▲
            </button>
            <button
              @click="moveItemDown(index)"
              :disabled="index === formData.length - 1"
              class="order-btn down-btn"
              title="下移"
            >
              ▼
            </button>
          </div>
          <div class="form-data-inputs">
            <input
              type="text"
              :value="item.key"
              @input="e => editItem(index, 'key', (e.target as HTMLInputElement).value)"
              placeholder="字段名"
              class="key-input"
            />
            <span class="separator">:</span>
            <input
              type="text"
              :value="item.value"
              @input="e => editItem(index, 'value', (e.target as HTMLInputElement).value)"
              placeholder="值"
              class="value-input"
            />
          </div>
          <button @click="removeItem(index)" class="remove-btn" title="删除此项">
            &times;
          </button>
        </div>
      </div>

      <div class="form-actions">
        <button
          @click="submitToBackend"
          :disabled="apiLoading || formData.length === 0"
          class="submit-btn"
        >
          <span v-if="apiLoading" class="loading-spinner"></span>
          {{ apiLoading ? '提交中...' : '提交数据' }}
        </button>
      </div>

      <div v-if="apiError" class="error">
        <strong>提交失败：</strong> {{ apiError }}
      </div>

      <div v-if="submitSuccess" class="success">
        <strong>成功！</strong> Strategy数据已成功提交到后端。
        <div v-if="validationResponse" class="validation-response">
          <strong>修改建议：</strong>
          <div class="markdown-content" v-html="parsedValidationResponse"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.strategy-page {
  padding: 1rem;
  max-width: 500px;
  font-family: Arial, sans-serif;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.browser-info {
  display: flex;
  align-items: center;
}

.browser-badge {
  background-color: #f0f0f0;
  color: #333;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.browser-badge.firefox {
  background-color: #ff9500;
  color: white;
}

.browser-badge.chrome {
  background-color: #4285f4;
  color: white;
}

.browser-badge.edge {
  background-color: #0078d4;
  color: white;
}

.back-btn {
  background: none;
  border: none;
  color: #2196F3;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.3rem 0.5rem;
  margin-right: 1rem;
  border-radius: 4px;
}

.back-btn:hover {
  background-color: #f0f0f0;
}

h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.info-box {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  color: #2e7d32;
}

.info-box p {
  margin: 0.5rem 0;
}

.info-box p:first-child {
  margin-top: 0;
}

.info-box p:last-child {
  margin-bottom: 0;
}

.controls {
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.parse-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.parse-btn:hover {
  background-color: #45a049;
}

.parse-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.clear-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.clear-btn:hover {
  background-color: #d32f2f;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  color: #f44336;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
  border-left: 4px solid #f44336;
}

.success {
  color: #4CAF50;
  margin-top: 1rem;
  padding: 0.5rem;
  background-color: #e8f5e9;
  border-radius: 4px;
  text-align: center;
  border-left: 4px solid #4CAF50;
}

.form-data-container {
  margin-top: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  background-color: #f9f9f9;
}

.form-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.1rem;
  margin: 0;
}

.add-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 0.3rem 0.8rem;
  font-size: 0.9rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.add-btn:hover {
  background-color: #0b7dda;
}

.form-data-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.empty-state {
  padding: 1rem;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-data-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.form-data-item:hover {
  border-color: #c8e6c9;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.item-order-buttons {
  display: flex;
  flex-direction: column;
  margin-right: 0.5rem;
}

.order-btn {
  background: none;
  border: none;
  color: #757575;
  font-size: 0.7rem;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.order-btn:hover:not(:disabled) {
  color: #4CAF50;
}

.order-btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.up-btn {
  margin-bottom: 2px;
}

.form-data-inputs {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.key-input, .value-input {
  padding: 0.3rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.key-input:focus, .value-input:focus {
  border-color: #4CAF50;
  outline: none;
}

.key-input {
  width: 40%;
  margin-right: 0.5rem;
  min-width: 80px;
}

.separator {
  margin: 0 0.5rem;
  color: #666;
  flex-shrink: 0;
}

.value-input {
  flex: 1;
  min-width: 80px;
}

.remove-btn {
  background: none;
  border: none;
  color: #f44336;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0.5rem;
  margin-left: 0.5rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  color: #d32f2f;
  background-color: #ffebee;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.submit-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover {
  background-color: #45a049;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.validation-response {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #4CAF50;
}

.validation-response :deep(p) {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.validation-response :deep(ul),
.validation-response :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.validation-response :deep(li) {
  margin: 0.25rem 0;
}

.validation-response :deep(strong) {
  color: #2e7d32;
}

.validation-response :deep(code) {
  background-color: #e8f5e9;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.validation-response :deep(blockquote) {
  margin: 0.5rem 0;
  padding-left: 1rem;
  border-left: 3px solid #c8e6c9;
  color: #546E7A;
}

.markdown-content {
  font-size: 0.95rem;
  color: #333;
}
</style>
