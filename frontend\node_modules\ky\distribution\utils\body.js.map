{"version": 3, "file": "body.js", "sourceRoot": "", "sources": ["../../source/utils/body.ts"], "names": [], "mappings": "AACA,OAAO,EAAC,qBAAqB,EAAC,MAAM,sBAAsB,CAAC;AAE3D,wDAAwD;AACxD,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,IAAsB,EAAU,EAAE;IAC7D,IAAI,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,CAAC,CAAC;IACV,CAAC;IAED,IAAI,IAAI,YAAY,QAAQ,EAAE,CAAC;QAC9B,gFAAgF;QAChF,IAAI,IAAI,GAAG,CAAC,CAAC;QAEb,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,IAAI,qBAAqB,CAAC;YAC9B,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,yCAAyC,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC;YACzF,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAChC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM;gBACxC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED,IAAI,IAAI,YAAY,IAAI,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC;IACxB,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;IAC9C,CAAC;IAED,IAAI,IAAI,YAAY,eAAe,EAAE,CAAC;QACrC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC;IACzD,CAAC;IAED,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QAC1B,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC;IAC1B,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;QAC/C,IAAI,CAAC;YACJ,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;QACpD,CAAC;QAAC,MAAM,CAAC;YACR,OAAO,CAAC,CAAC;QACV,CAAC;IACF,CAAC;IAED,OAAO,CAAC,CAAC,CAAC,yCAAyC;AACpD,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,QAAkB,EAAE,kBAAiD,EAAE,EAAE;IACvG,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;IACvE,IAAI,gBAAgB,GAAG,CAAC,CAAC;IAEzB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC7B,IAAI,kBAAkB,EAAE,CAAC;YACxB,kBAAkB,CAAC,EAAC,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAC,EAAE,IAAI,UAAU,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,OAAO,IAAI,QAAQ,CAClB,IAAI,EACJ;YACC,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;SACzB,CACD,CAAC;IACH,CAAC;IAED,OAAO,IAAI,QAAQ,CAClB,IAAI,cAAc,CAAC;QAClB,KAAK,CAAC,KAAK,CAAC,UAAU;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAK,CAAC,SAAS,EAAE,CAAC;YAE1C,IAAI,kBAAkB,EAAE,CAAC;gBACxB,kBAAkB,CAAC,EAAC,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,UAAU,EAAC,EAAE,IAAI,UAAU,EAAE,CAAC,CAAC;YACrF,CAAC;YAED,KAAK,UAAU,IAAI;gBAClB,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC1C,IAAI,IAAI,EAAE,CAAC;oBACV,UAAU,CAAC,KAAK,EAAE,CAAC;oBACnB,OAAO;gBACR,CAAC;gBAED,IAAI,kBAAkB,EAAE,CAAC;oBACxB,gBAAgB,IAAI,KAAK,CAAC,UAAU,CAAC;oBACrC,MAAM,OAAO,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,UAAU,CAAC;oBACrE,kBAAkB,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAC,EAAE,KAAK,CAAC,CAAC;gBACpE,CAAC;gBAED,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1B,MAAM,IAAI,EAAE,CAAC;YACd,CAAC;YAED,MAAM,IAAI,EAAE,CAAC;QACd,CAAC;KACD,CAAC,EACF;QACC,MAAM,EAAE,QAAQ,CAAC,MAAM;QACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;QAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;KACzB,CACD,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,OAAgB,EAAE,gBAA6C,EAAE,EAAE;IAChG,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,gBAAgB,GAAG,CAAC,CAAC;IAEzB,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;QAC3B,yCAAyC;QACzC,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,IAAI,cAAc,CAAC;YACxB,KAAK,CAAC,KAAK,CAAC,UAAU;gBACrB,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,YAAY,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAK,CAAC,SAAS,EAAE,CAAC;gBAEtH,KAAK,UAAU,IAAI;oBAClB,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBAC1C,IAAI,IAAI,EAAE,CAAC;wBACV,+DAA+D;wBAC/D,IAAI,gBAAgB,EAAE,CAAC;4BACtB,gBAAgB,CAAC,EAAC,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAC,EAAE,IAAI,UAAU,EAAE,CAAC,CAAC;wBACxH,CAAC;wBAED,UAAU,CAAC,KAAK,EAAE,CAAC;wBACnB,OAAO;oBACR,CAAC;oBAED,gBAAgB,IAAI,KAAK,CAAC,UAAU,CAAC;oBACrC,IAAI,OAAO,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,UAAU,CAAC;oBACnE,IAAI,UAAU,GAAG,gBAAgB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;wBACpD,OAAO,GAAG,IAAI,CAAC;oBAChB,CAAC;oBAED,IAAI,gBAAgB,EAAE,CAAC;wBACtB,gBAAgB,CAAC,EAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,gBAAgB,EAAE,UAAU,EAAC,EAAE,KAAK,CAAC,CAAC;oBAC9F,CAAC;oBAED,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC1B,MAAM,IAAI,EAAE,CAAC;gBACd,CAAC;gBAED,MAAM,IAAI,EAAE,CAAC;YACd,CAAC;SACD,CAAC;KACF,CAAC,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import type {Options} from '../types/options.js';\nimport {usualFormBoundarySize} from '../core/constants.js';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const getBodySize = (body?: BodyInit | null): number => {\n\tif (!body) {\n\t\treturn 0;\n\t}\n\n\tif (body instanceof FormData) {\n\t\t// This is an approximation, as FormData size calculation is not straightforward\n\t\tlet size = 0;\n\n\t\tfor (const [key, value] of body) {\n\t\t\tsize += usualFormBoundarySize;\n\t\t\tsize += new TextEncoder().encode(`Content-Disposition: form-data; name=\"${key}\"`).length;\n\t\t\tsize += typeof value === 'string'\n\t\t\t\t? new TextEncoder().encode(value).length\n\t\t\t\t: value.size;\n\t\t}\n\n\t\treturn size;\n\t}\n\n\tif (body instanceof Blob) {\n\t\treturn body.size;\n\t}\n\n\tif (body instanceof ArrayBuffer) {\n\t\treturn body.byteLength;\n\t}\n\n\tif (typeof body === 'string') {\n\t\treturn new TextEncoder().encode(body).length;\n\t}\n\n\tif (body instanceof URLSearchParams) {\n\t\treturn new TextEncoder().encode(body.toString()).length;\n\t}\n\n\tif ('byteLength' in body) {\n\t\treturn (body).byteLength;\n\t}\n\n\tif (typeof body === 'object' && body !== null) {\n\t\ttry {\n\t\t\tconst jsonString = JSON.stringify(body);\n\t\t\treturn new TextEncoder().encode(jsonString).length;\n\t\t} catch {\n\t\t\treturn 0;\n\t\t}\n\t}\n\n\treturn 0; // Default case, unable to determine size\n};\n\nexport const streamResponse = (response: Response, onDownloadProgress: Options['onDownloadProgress']) => {\n\tconst totalBytes = Number(response.headers.get('content-length')) || 0;\n\tlet transferredBytes = 0;\n\n\tif (response.status === 204) {\n\t\tif (onDownloadProgress) {\n\t\t\tonDownloadProgress({percent: 1, totalBytes, transferredBytes}, new Uint8Array());\n\t\t}\n\n\t\treturn new Response(\n\t\t\tnull,\n\t\t\t{\n\t\t\t\tstatus: response.status,\n\t\t\t\tstatusText: response.statusText,\n\t\t\t\theaders: response.headers,\n\t\t\t},\n\t\t);\n\t}\n\n\treturn new Response(\n\t\tnew ReadableStream({\n\t\t\tasync start(controller) {\n\t\t\t\tconst reader = response.body!.getReader();\n\n\t\t\t\tif (onDownloadProgress) {\n\t\t\t\t\tonDownloadProgress({percent: 0, transferredBytes: 0, totalBytes}, new Uint8Array());\n\t\t\t\t}\n\n\t\t\t\tasync function read() {\n\t\t\t\t\tconst {done, value} = await reader.read();\n\t\t\t\t\tif (done) {\n\t\t\t\t\t\tcontroller.close();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (onDownloadProgress) {\n\t\t\t\t\t\ttransferredBytes += value.byteLength;\n\t\t\t\t\t\tconst percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n\t\t\t\t\t\tonDownloadProgress({percent, transferredBytes, totalBytes}, value);\n\t\t\t\t\t}\n\n\t\t\t\t\tcontroller.enqueue(value);\n\t\t\t\t\tawait read();\n\t\t\t\t}\n\n\t\t\t\tawait read();\n\t\t\t},\n\t\t}),\n\t\t{\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t\theaders: response.headers,\n\t\t},\n\t);\n};\n\nexport const streamRequest = (request: Request, onUploadProgress: Options['onUploadProgress']) => {\n\tconst totalBytes = getBodySize(request.body);\n\tlet transferredBytes = 0;\n\n\treturn new Request(request, {\n\t\t// @ts-expect-error - Types are outdated.\n\t\tduplex: 'half',\n\t\tbody: new ReadableStream({\n\t\t\tasync start(controller) {\n\t\t\t\tconst reader = request.body instanceof ReadableStream ? request.body.getReader() : new Response('').body!.getReader();\n\n\t\t\t\tasync function read() {\n\t\t\t\t\tconst {done, value} = await reader.read();\n\t\t\t\t\tif (done) {\n\t\t\t\t\t\t// Ensure 100% progress is reported when the upload is complete\n\t\t\t\t\t\tif (onUploadProgress) {\n\t\t\t\t\t\t\tonUploadProgress({percent: 1, transferredBytes, totalBytes: Math.max(totalBytes, transferredBytes)}, new Uint8Array());\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tcontroller.close();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\ttransferredBytes += value.byteLength;\n\t\t\t\t\tlet percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n\t\t\t\t\tif (totalBytes < transferredBytes || percent === 1) {\n\t\t\t\t\t\tpercent = 0.99;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (onUploadProgress) {\n\t\t\t\t\t\tonUploadProgress({percent: Number(percent.toFixed(2)), transferredBytes, totalBytes}, value);\n\t\t\t\t\t}\n\n\t\t\t\t\tcontroller.enqueue(value);\n\t\t\t\t\tawait read();\n\t\t\t\t}\n\n\t\t\t\tawait read();\n\t\t\t},\n\t\t}),\n\t});\n};\n"]}